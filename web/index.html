<!DOCTYPE html>
<html>
<head>
  <!--
    If you are serving your web app in a path other than the root, change the
    href value below to reflect the base path you are serving from.

    The path provided below has to start and end with a slash "/" in order for
    it to work correctly.

    For more details:
    * https://developer.mozilla.org/en-US/docs/Web/HTML/Element/base

    This is a placeholder for base href that will be replaced by the value of
    the `--base-href` argument provided to `flutter build`.
  -->
  <base href="$FLUTTER_BASE_HREF">

  <meta charset="UTF-8">
  <meta content="IE=Edge" http-equiv="X-UA-Compatible">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta name="description" content="Idea2App Vendor - Download the app for your platform.">
  <meta name="keywords" content="Idea2App, Vendor, App, iOS, Android, Download">
  <meta name="author" content="Idea2App">

  <!-- Preconnect to external domains for faster redirects -->
  <link rel="preconnect" href="https://apps.apple.com">
  <link rel="preconnect" href="https://play.google.com">
  <link rel="preconnect" href="https://vendor.idea2app.tech">

  <!-- iOS meta tags & icons -->
  <meta name="apple-mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="black">
  <meta name="apple-mobile-web-app-title" content="idea2app_vendor_app">
  <link rel="apple-touch-icon" href="icons/Icon-192.png">

  <!-- Favicon -->
  <link rel="icon" type="image/png" href="favicon.png"/>

  <title>Idea2App Vendor</title>
  <link rel="manifest" href="manifest.json">

  <script>
    // The value below is injected by flutter build, do not touch.
    var serviceWorkerVersion = null;
  </script>

  <!-- Fast Platform Detection and Redirection Script -->
  <script>
    (function() {
      // Get user agent for platform detection
      const userAgent = navigator.userAgent || navigator.vendor || window.opera;
      const platform = navigator.platform;

      // Platform detection
      const isIOS = /iPad|iPhone|iPod/.test(userAgent) && !window.MSStream;
      const isAndroid = /android/i.test(userAgent);
      const isMac = /Mac/.test(platform) && !isIOS;
      const isWindows = /Win/.test(platform);

      // More specific iOS detection
      const isIPad = /iPad/.test(userAgent) || (navigator.platform === 'MacIntel' && navigator.maxTouchPoints > 1);
      const isIPhone = /iPhone/.test(userAgent);

      // Redirect URLs
      const links = {
        ipad: 'https://apps.apple.com/us/app/idea2app-vendor/id6479177341?platform=ipad',
        iphone: 'https://apps.apple.com/us/app/idea2app-vendor/id6479177341?platform=iphone',
        android: 'https://play.google.com/store/apps/details?id=com.idea2app.vendor_app',
        website: 'https://vendor.idea2app.tech'
      };

      // Determine redirect URL
      let redirectUrl = null;

      if (isIPad) {
        redirectUrl = links.ipad;
      } else if (isIPhone) {
        redirectUrl = links.iphone;
      } else if (isMac) {
        redirectUrl = links.ipad; // Mac users get iPad app store
      } else if (isAndroid) {
        redirectUrl = links.android;
      } else {
        redirectUrl = links.website; // Windows and other platforms
      }

      // Immediate redirect for maximum speed
      if (redirectUrl) {
        window.location.replace(redirectUrl);
      }
    })();
  </script>

  <!-- This script adds the flutter initialization JS code -->
  <script src="flutter.js" defer></script>
</head>
<body>
  <!-- Fallback content for users with JavaScript disabled -->
  <noscript>
    <div style="text-align: center; padding: 50px; font-family: Arial, sans-serif;">
      <h1>Idea2App Vendor</h1>
      <p>Please enable JavaScript for automatic redirection, or choose your platform:</p>
      <div style="margin: 20px 0;">
        <a href="https://apps.apple.com/us/app/idea2app-vendor/id6479177341?platform=ipad"
           style="display: inline-block; margin: 10px; padding: 15px 25px; background: #007AFF; color: white; text-decoration: none; border-radius: 8px;">
          Download for iPad/Mac
        </a>
        <a href="https://apps.apple.com/us/app/idea2app-vendor/id6479177341?platform=iphone"
           style="display: inline-block; margin: 10px; padding: 15px 25px; background: #007AFF; color: white; text-decoration: none; border-radius: 8px;">
          Download for iPhone
        </a>
        <a href="https://play.google.com/store/apps/details?id=com.idea2app.vendor_app"
           style="display: inline-block; margin: 10px; padding: 15px 25px; background: #34A853; color: white; text-decoration: none; border-radius: 8px;">
          Download for Android
        </a>
        <a href="https://vendor.idea2app.tech"
           style="display: inline-block; margin: 10px; padding: 15px 25px; background: #666; color: white; text-decoration: none; border-radius: 8px;">
          Visit Website
        </a>
      </div>
    </div>
  </noscript>

  <script>
    window.addEventListener('load', function(ev) {
      // Download main.dart.js
      _flutter.loader.loadEntrypoint({
        serviceWorker: {
          serviceWorkerVersion: serviceWorkerVersion,
        },
        onEntrypointLoaded: function(engineInitializer) {
          engineInitializer.initializeEngine().then(function(appRunner) {
            appRunner.runApp();
          });
        }
      });
    });
  </script>
</body>
</html>
