{"configurations": [{"directories": [{"build": ".", "jsonFile": "directory-.-RelWithDebInfo-f5ebdc15457944623624.json", "minimumCMakeVersion": {"string": "3.10.0"}, "projectIndex": 0, "source": "."}], "name": "RelWithDebInfo", "projects": [{"directoryIndexes": [0], "name": "Project"}], "targets": []}], "kind": "codemodel", "paths": {"build": "/Users/<USER>/Flutter-Projects/Idea2App/idea2app_vendor/android/app/.cxx/RelWithDebInfo/5e645l3e/x86_64", "source": "/Users/<USER>/development/flutter/packages/flutter_tools/gradle/src/main/groovy"}, "version": {"major": 2, "minor": 3}}