                        -H/Users/<USER>/.shorebird/bin/cache/flutter/3f9cefb45389b72ff073ddf305fe0939f822143b/packages/flutter_tools/gradle/src/main/groovy
-DCMAKE_SYSTEM_NAME=Android
-DCMAKE_EXPORT_COMPILE_COMMANDS=ON
-DCMAKE_SYSTEM_VERSION=23
-DANDROID_PLATFORM=android-23
-DANDROID_ABI=armeabi-v7a
-DCMAKE_ANDROID_ARCH_ABI=armeabi-v7a
-DANDROID_NDK=/Users/<USER>/Library/Android/sdk/ndk/28.0.13004108
-DCMAKE_ANDROID_NDK=/Users/<USER>/Library/Android/sdk/ndk/28.0.13004108
-DCMAKE_TOOLCHAIN_FILE=/Users/<USER>/Library/Android/sdk/ndk/28.0.13004108/build/cmake/android.toolchain.cmake
-DCMAKE_MAKE_PROGRAM=/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja
-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=/Users/<USER>/Flutter-Projects/Idea2App/idea2app_vendor/build/app/intermediates/cxx/RelWithDebInfo/1p6wh6p4/obj/armeabi-v7a
-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=/Users/<USER>/Flutter-Projects/Idea2App/idea2app_vendor/build/app/intermediates/cxx/RelWithDebInfo/1p6wh6p4/obj/armeabi-v7a
-DCMAKE_BUILD_TYPE=RelWithDebInfo
-B/Users/<USER>/Flutter-Projects/Idea2App/idea2app_vendor/android/app/.cxx/RelWithDebInfo/1p6wh6p4/armeabi-v7a
-GNinja
-Wno-dev
--no-warn-unused-cli
                        Build command args: []
                        Version: 2