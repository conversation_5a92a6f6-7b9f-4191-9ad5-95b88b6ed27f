{"buildFiles": ["/Users/<USER>/development/flutter/packages/flutter_tools/gradle/src/main/groovy/CMakeLists.txt"], "cleanCommandsComponents": [["/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja", "-C", "/Users/<USER>/Flutter-Projects/Idea2App/idea2app_vendor/android/app/.cxx/Debug/495p6p6z/x86", "clean"]], "buildTargetsCommandComponents": ["/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja", "-C", "/Users/<USER>/Flutter-Projects/Idea2App/idea2app_vendor/android/app/.cxx/Debug/495p6p6z/x86", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}, "toolchains": {"toolchain": {"cCompilerExecutable": "/Users/<USER>/Library/Android/sdk/ndk/28.0.13004108/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang.lld", "cppCompilerExecutable": "/Users/<USER>/Library/Android/sdk/ndk/28.0.13004108/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++.lld"}}, "cFileExtensions": [], "cppFileExtensions": []}