# Idea2App Vendor Link Converter

A fast and efficient link converter that automatically redirects users to the appropriate Idea2App Vendor app download based on their platform.

## Features

- **Ultra-fast redirection** using JavaScript in HTML (executes before Flutter loads)
- **Platform detection** for iOS (iPad/iPhone), macOS, Android, and other platforms
- **Fallback mechanisms** with Flutter-based detection and manual links
- **No-JavaScript support** with manual download links

## Platform Redirections

| Platform | Redirect URL |
|----------|-------------|
| iPad | `https://apps.apple.com/us/app/idea2app-vendor/id6479177341?platform=ipad` |
| iPhone | `https://apps.apple.com/us/app/idea2app-vendor/id6479177341?platform=iphone` |
| Mac | `https://apps.apple.com/us/app/idea2app-vendor/id6479177341?platform=ipad` |
| Android | `https://play.google.com/store/apps/details?id=com.idea2app.vendor_app` |
| Windows/Others | `https://vendor.idea2app.tech` |

## How It Works

### 1. HTML-Level Redirection (Fastest)
- JavaScript in `web/index.html` detects platform immediately
- Redirects before Flutter app loads for maximum speed
- Uses `window.location.replace()` for instant redirection

### 2. Flutter Fallback
- If HTML redirection fails, Flutter app loads
- Uses `universal_platform` package for platform detection
- Uses `url_launcher` for opening platform-specific URLs

### 3. Manual Fallback
- For users with JavaScript disabled
- Shows manual download buttons for all platforms
- Styled with inline CSS for immediate display

## Building and Deployment

```bash
# Install dependencies
flutter pub get

# Build for web
flutter build web --release

# Deploy the contents of build/web/ to your web server
```

## Performance Optimizations

- **Preconnect links** to external domains for faster redirects
- **Minimal HTML** with inline JavaScript for instant execution
- **No external dependencies** in the redirection logic
- **Tree-shaken Flutter build** for smaller bundle size

## Testing

The app will redirect immediately when accessed from different platforms:
- Test on iOS devices (iPad/iPhone)
- Test on Android devices
- Test on desktop browsers (Mac/Windows)
- Test with JavaScript disabled

## License

This project is part of the Idea2App ecosystem.
# idea2app_vendor
