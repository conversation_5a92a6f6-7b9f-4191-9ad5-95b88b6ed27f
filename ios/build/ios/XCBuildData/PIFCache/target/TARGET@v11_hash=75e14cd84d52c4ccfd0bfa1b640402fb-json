{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98cf53fb3e058c578225354f2333013970", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/DKImagePickerController", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "DKImagePickerController", "INFOPLIST_FILE": "Target Support Files/DKImagePickerController/ResourceBundle-DK<PERSON>magePickerController-DKImagePickerController-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_NAME": "DKImagePickerController", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e981f3d017de06284382744d4f525e54915", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e982d6361b20b4c121b9d8158dceb18d358", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/DKImagePickerController", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "DKImagePickerController", "INFOPLIST_FILE": "Target Support Files/DKImagePickerController/ResourceBundle-DK<PERSON>magePickerController-DKImagePickerController-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "PRODUCT_NAME": "DKImagePickerController", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e982069578567192418e2a1012a8e84b886", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e982d6361b20b4c121b9d8158dceb18d358", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/DKImagePickerController", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "DKImagePickerController", "INFOPLIST_FILE": "Target Support Files/DKImagePickerController/ResourceBundle-DK<PERSON>magePickerController-DKImagePickerController-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "PRODUCT_NAME": "DKImagePickerController", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e984aea10b3a81d7e8538784bf3058d0aba", "name": "Release"}], "buildPhases": [{"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e9850e5e0a99891ff9afaf5603ddc3e23f4", "type": "com.apple.buildphase.sources"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98223adc8ed655983b69bb9393f9ff3851", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98d14cc137387482c86fdc9ba168fa8114", "guid": "bfdfe7dc352907fc980b868725387e988f2729ef24447ad77e0cd7e5aad0c2f4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c176b9d56cc10e14a65e0a86f31d109c", "guid": "bfdfe7dc352907fc980b868725387e98ea263cb6a949f21cd316986575ae554f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fa7bbed1ec70d565e87a9dea3f070e3c", "guid": "bfdfe7dc352907fc980b868725387e988982900b86dd3d31bd44617a4834dd19"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a4335801cc05fc818a6ee753bb40bc2b", "guid": "bfdfe7dc352907fc980b868725387e982b6b4d6b1cc0c6970bac7611c82ff974"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98db6cf4cafe88b6789fab72a6526b89c8", "guid": "bfdfe7dc352907fc980b868725387e9886a8b51a4bbc1e4ffde75f12b2d51c74"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c3c4bc4ca8778ca0d8d004a27aa377fa", "guid": "bfdfe7dc352907fc980b868725387e98d2dfa1d06827a29deb7eb2f19014fbe6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983c161987328f2334306c06d507213b85", "guid": "bfdfe7dc352907fc980b868725387e98338f32877df7b0115ca0717c3a5ea9a2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a73ccec5dcf8a8c9138387f85f393a90", "guid": "bfdfe7dc352907fc980b868725387e9832856f7eeb59bbc31ca4ec885f41b9f4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b86fe8b9f87f614bf4a0d61f87a128c9", "guid": "bfdfe7dc352907fc980b868725387e989b1668b7c803b293d355228f801e7aae"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98de93e29d247438e6e2fc3486f730307b", "guid": "bfdfe7dc352907fc980b868725387e982b122ebfdbe6505371ef18d2c283916f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98498cfc89bd8e3718793a4d325ab921ba", "guid": "bfdfe7dc352907fc980b868725387e985ff768d6584cc569f2ab5fe4b12bfb3a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989a612917b170c1a405026a27412cd056", "guid": "bfdfe7dc352907fc980b868725387e9813db1f0a85fb1f4b97b06f59e66ff11e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9817fd8c36da9c4ef4eed4b0343eaf10e3", "guid": "bfdfe7dc352907fc980b868725387e98e11d65e9fcaf5ab82025b4418e7667b1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9838831c6c5abcc58037bf9e02acf8c24d", "guid": "bfdfe7dc352907fc980b868725387e98864ecf30e2a758a0f9c6dad8f0e5d428"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d2d89e10cc9f287867f27270a809144a", "guid": "bfdfe7dc352907fc980b868725387e98a3307ce0bf0401a867436d9929848336"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985c7cd12572088b0eba0b59980631bb4e", "guid": "bfdfe7dc352907fc980b868725387e9840ada13ffc7dead979090fadb3ede432"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fb977972f1001e184108eaf47e12e21a", "guid": "bfdfe7dc352907fc980b868725387e98bae5821c9bea0c2d804eea1e9bf39d6a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fb98ad246d7048be1e5106780391dfa4", "guid": "bfdfe7dc352907fc980b868725387e98e92468b1550a2568d9726bfa7304d140"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c7fedc5f2768dc9882e3547b5072215a", "guid": "bfdfe7dc352907fc980b868725387e98f6b4d6fcc31a03edfc03c251ab7f8c17"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9815976f0c7b4c897b47cdb4532d844611", "guid": "bfdfe7dc352907fc980b868725387e9850b9a417786d298a0dc6db6ca000a093"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986897fe850392712121967b16ceb91cef", "guid": "bfdfe7dc352907fc980b868725387e98cfa3a5f2acb5d4a6c0a3efc2c800a814"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988edc8de321086752078871105d304dae", "guid": "bfdfe7dc352907fc980b868725387e98b0f4ba0e6c985f2907d3a5bb702e9903"}], "guid": "bfdfe7dc352907fc980b868725387e982cfa6093881f08b60c13718f3750e766", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [], "guid": "bfdfe7dc352907fc980b868725387e9898fccba7a2febdedb43dddbf2e949fc3", "name": "DKImagePickerController-DKImagePickerController", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98ab5e1f747dfe477b655528b07584898d", "name": "DKImagePickerController.bundle", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.bundle", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 0}], "type": "standard"}