{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9874c8da45b13468f284fe445761c4dc94", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/.shorebird/bin/cache/flutter/0ac69de92c7201830d6a687a44fa0d34f6003f90/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/.shorebird/bin/cache/flutter/0ac69de92c7201830d6a687a44fa0d34f6003f90/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/permission_handler_apple/permission_handler_apple-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "permission_handler_apple", "PRODUCT_NAME": "permission_handler_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98519c0f1aabf34eacbf6f755b813f1496", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9857f66d29cb0f675c3c574686c3b05f81", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/.shorebird/bin/cache/flutter/0ac69de92c7201830d6a687a44fa0d34f6003f90/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/.shorebird/bin/cache/flutter/0ac69de92c7201830d6a687a44fa0d34f6003f90/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/permission_handler_apple/permission_handler_apple-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "permission_handler_apple", "PRODUCT_NAME": "permission_handler_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98ec62137b0ee28ca4265944856877be27", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9857f66d29cb0f675c3c574686c3b05f81", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/.shorebird/bin/cache/flutter/0ac69de92c7201830d6a687a44fa0d34f6003f90/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/.shorebird/bin/cache/flutter/0ac69de92c7201830d6a687a44fa0d34f6003f90/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/permission_handler_apple/permission_handler_apple-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "permission_handler_apple", "PRODUCT_NAME": "permission_handler_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9832ebf16d88dad2729e444c32094aa46a", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e983b7cae0ed50505d65bbc17db6a1fe820", "guid": "bfdfe7dc352907fc980b868725387e98fbbe543b71214f4509d8c92248a60d08", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ed02434fc50c083a91a7d5ca034779c6", "guid": "bfdfe7dc352907fc980b868725387e98471ff3f9f1b2efe128b7c2d23d239746", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b05005648fced66525584e664d16982d", "guid": "bfdfe7dc352907fc980b868725387e98b76c0130342994e0dd30f1d68cb8abfd", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98febb1f7c8cf40c915a833857f422b2ca", "guid": "bfdfe7dc352907fc980b868725387e98b026e65d04846ce716b6283013c9b796", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aee278f1bf0f39cd3a1d6da27f131033", "guid": "bfdfe7dc352907fc980b868725387e985e0782c485d14f7513f47a5628a6b51b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983884072414e3bfa2d98dfc49b6b3a2fe", "guid": "bfdfe7dc352907fc980b868725387e980dc402cd31c13f836903d7124fb9cd77", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98411496b14377311f2a36be215a826348", "guid": "bfdfe7dc352907fc980b868725387e989c0fc48a74ff41a0a9254c97ece8daaf", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a8b5ab3ddb99b3a4845298241cfedc16", "guid": "bfdfe7dc352907fc980b868725387e9861fa11d4d6cf54947eab5534b60f0d93", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9842fde5f577b13146fc13394f1945b3f9", "guid": "bfdfe7dc352907fc980b868725387e985abce06578652c4d8dc3d24c702bd3c0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c8de8b5a819a51b0718b1514989b4b49", "guid": "bfdfe7dc352907fc980b868725387e98b8c7585471bcf10789da28bb2a96c405", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9845a1162b696c50b45b6b93583d48f762", "guid": "bfdfe7dc352907fc980b868725387e98e293850950354b35a2786639c3e3939e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9803597070d899aca38e3cb6900e884c69", "guid": "bfdfe7dc352907fc980b868725387e980ef10060e448131985daa2036af5ae3e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a9e8a76e1d23e11c507e68b503c8d6cc", "guid": "bfdfe7dc352907fc980b868725387e98dc02dbc796bf1a1c79ffc8c7bf3cea72", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9875d09fbf2cf95939b02e51bef6ffbf6b", "guid": "bfdfe7dc352907fc980b868725387e98c1277031b6540d3602e8c79a5b369c19", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983a1ff9cf1d59553c2d213af27ced85ed", "guid": "bfdfe7dc352907fc980b868725387e98b129ca47461bd3877af22b5d56ec22fd", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9889d2693f35eb654036ab5719167cbd6b", "guid": "bfdfe7dc352907fc980b868725387e98808ab512e47ea5c54aad6b2807ee7bef", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98472207627756235b99728bf3fc633f2c", "guid": "bfdfe7dc352907fc980b868725387e98c191f1ff744a395685e3770c58c824f9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984f7f92399b9cb442b99b63aaf420a1d6", "guid": "bfdfe7dc352907fc980b868725387e982bc1b5cee954a27e7861f672675db061", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9824df930bc3ca1730bf30ceb9f4828135", "guid": "bfdfe7dc352907fc980b868725387e984e7a5baa4ea128e81cc7b93ea895a0bd", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d0da8e8a6cf512a672560f54aef4f883", "guid": "bfdfe7dc352907fc980b868725387e986f2e44c4deb448e91b7f9dcbd9877023", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e86408337faae7c9d4f167561d97fc85", "guid": "bfdfe7dc352907fc980b868725387e9878438d158bdc6c70e3cc6f1422a1fd5b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98124ee051642b466297230230167288d8", "guid": "bfdfe7dc352907fc980b868725387e9896c5db8f09f591e14135d056fe937d39", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98070622486163f07f154d3577c2155b05", "guid": "bfdfe7dc352907fc980b868725387e98ed733eeeb4cf442e79221b43ec6ab543", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e986a03fa67e33d48dce94be2b8eb2259ec", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e988557d588bf11c18c64958f11cb798849", "guid": "bfdfe7dc352907fc980b868725387e9863f74156f992cef9610ea0027b158797"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c4fcffd1f074a4812052c06b04e1d242", "guid": "bfdfe7dc352907fc980b868725387e9817dcb77d7f539f0ba7cf6bba23ab025a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98db799a3ed14b1827ce76f24ee5f963c6", "guid": "bfdfe7dc352907fc980b868725387e98430e4d66334ef552e35896edf0800fd4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98330681e7f55a432ef7c1968740eef4c4", "guid": "bfdfe7dc352907fc980b868725387e98ac1c4a6aecb934ba3f7ce5f4b7a5ce10"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9841cc9f2bf4e71af6dfd137d269e0bd12", "guid": "bfdfe7dc352907fc980b868725387e980d88881e8f1864ad44addeb4b546f957"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9810bb3ff8073b00ccc4ae49d117b0db23", "guid": "bfdfe7dc352907fc980b868725387e981d2fe54f8f024aea0393135c6622c36f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982e37646242883703c0abba698c06729d", "guid": "bfdfe7dc352907fc980b868725387e9875a7bbe49ed36b7b4958102bd487c186"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ef153e5f6221d72765c288e0b1feabf3", "guid": "bfdfe7dc352907fc980b868725387e98c2fd66d30ed9c469a6f9e66511104826"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98da215091296694cc722fc08742500715", "guid": "bfdfe7dc352907fc980b868725387e986898536a32f1ba314b25df11fafa9a37"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980599dc79411d8dbe95e71616ab28cb2e", "guid": "bfdfe7dc352907fc980b868725387e989f1c87cce3ba04c5b99d6bc86b996ce0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bcc1168f20c1c5063675865213de5dbf", "guid": "bfdfe7dc352907fc980b868725387e98892efd917ae976f6c02ccd36be495e89"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c448b0adf9463fa3ee0b298ea05940fb", "guid": "bfdfe7dc352907fc980b868725387e988f6dd6984240ec7b3e112a512bcbd51f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bd1333d86c7a671b6791ac36dd8737be", "guid": "bfdfe7dc352907fc980b868725387e981850b344567e5543096f0a83d9eac0b2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98debf4f23770ff5f2bdd942af69134d9f", "guid": "bfdfe7dc352907fc980b868725387e98111a96713a87f9cb39e8152dd6dde744"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fc9f0b4d16572f988dd69cb0cfa85439", "guid": "bfdfe7dc352907fc980b868725387e985b7f8093353d96741cf9ecdce14f0a8f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dc177170ef4ab90b0e92b1d0cf8b40e2", "guid": "bfdfe7dc352907fc980b868725387e98a735fa885496d6e8c24d59f0cb2548ec"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9863b374252125bf533f300e3862717d96", "guid": "bfdfe7dc352907fc980b868725387e9837a1e7be82ce3385de98f18fd5990f89"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a2435768744bb70fdf73f79724254e34", "guid": "bfdfe7dc352907fc980b868725387e9892af032a9b1b9cd1201f3d2ed313dfed"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9888dbff93bec39275ffcb7f5545133728", "guid": "bfdfe7dc352907fc980b868725387e9826eaae4dc2c7871e7fbd53b8b7eb1b96"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98070ec4cd5d4826748f78b67fc010fe3b", "guid": "bfdfe7dc352907fc980b868725387e98241a83cbc6157ce812894eb73152a9f4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985c9d075b7207f82799b2410225415665", "guid": "bfdfe7dc352907fc980b868725387e98160b038fa4d62a21aeddf4fe7e39ba83"}], "guid": "bfdfe7dc352907fc980b868725387e98be6229230a4715433df2f8e74fbafc5b", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9823eca4b3b33af74b846381c4acc45081", "guid": "bfdfe7dc352907fc980b868725387e980797c2152e50219ee4196549bb34f857"}], "guid": "bfdfe7dc352907fc980b868725387e984d290968aff9eafa4ed5b85c80a8c610", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98fa0d11ed0b4e1a85c13d68e37d1547e0", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e9802f35ab680609a626ebd2ddd692a3822", "name": "permission_handler_apple-permission_handler_apple_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98ef10255b706f98e1e88fae00855b0968", "name": "permission_handler_apple", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98f8f53f8ba4165e76c7481b24262177ed", "name": "permission_handler_apple.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}