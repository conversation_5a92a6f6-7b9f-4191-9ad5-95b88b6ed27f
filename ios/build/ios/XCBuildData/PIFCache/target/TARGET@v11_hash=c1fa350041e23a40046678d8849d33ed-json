{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98bb90d5aaf8d9003f897892d47b28aaa8", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/nanopb/nanopb-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/nanopb/nanopb-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/nanopb/nanopb.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "nanopb", "PRODUCT_NAME": "nanopb", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98a9b6bda2c30b89d1d0a4be33d138f45e", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98f8c7bded849c68dacc11efb18a0569c9", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/nanopb/nanopb-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/nanopb/nanopb-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/nanopb/nanopb.modulemap", "PRODUCT_MODULE_NAME": "nanopb", "PRODUCT_NAME": "nanopb", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9842ae298ccac3bea9f5a59d9588ba5537", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98f8c7bded849c68dacc11efb18a0569c9", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/nanopb/nanopb-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/nanopb/nanopb-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/nanopb/nanopb.modulemap", "PRODUCT_MODULE_NAME": "nanopb", "PRODUCT_NAME": "nanopb", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e981854f61fe581aae0e202e91cafbe70b6", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e985cc83f23e44e19a468de4ca5cce8f7d2", "guid": "bfdfe7dc352907fc980b868725387e9835e13a6553ec5e622616d53fb27deb1a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985f4e5f4f3c2ccecdd3df33d83ac3bcbb", "guid": "bfdfe7dc352907fc980b868725387e984948ec0d60df29118e56efcfc558eff8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f8ac557c0784a8f20a54161a4a7e073e", "guid": "bfdfe7dc352907fc980b868725387e980968dec621bdad027dc78de859304692", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ef89939d0befbd9942fd681c42a7af10", "guid": "bfdfe7dc352907fc980b868725387e98d3bc8c620c4be1c3d59a95968fbcb2c7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988407901bf1086b891d55331f08189d6c", "guid": "bfdfe7dc352907fc980b868725387e982e2e8d9e5973add7202ed039d2293ed0", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98af2d3e724a5206853ba49e7fc664fad8", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98b8252eb343259fdd631e33e047ed100a", "guid": "bfdfe7dc352907fc980b868725387e98568bd77bd4b092b3ceadfd8de8d3a299"}, {"additionalCompilerOptions": "-fno-objc-arc -fno-objc-arc -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e986050e165d137be094dd3b48dc8d58b28", "guid": "bfdfe7dc352907fc980b868725387e9886bef152633fc9a019c6c4916cb33b35"}, {"additionalCompilerOptions": "-fno-objc-arc -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e983c8a2b14f386f9302410046a19e541c1", "guid": "bfdfe7dc352907fc980b868725387e989a299e78673368b7ae7106b4c7cbee56"}, {"additionalCompilerOptions": "-fno-objc-arc -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e9873f828163ef53bd4b9d238e0f9d7d8fe", "guid": "bfdfe7dc352907fc980b868725387e98a9f2cb1fdf72b9d350cf06a6810a8576"}], "guid": "bfdfe7dc352907fc980b868725387e982efc0b23de59ba76357197d21ee1f4f4", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9823eca4b3b33af74b846381c4acc45081", "guid": "bfdfe7dc352907fc980b868725387e98eac27c2967676a0f87430348c81a92ea"}], "guid": "bfdfe7dc352907fc980b868725387e984e31e5f9f549db6f56ff714886c64cff", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e983c661efbf9597781e11390c796df4197", "targetReference": "bfdfe7dc352907fc980b868725387e98c9e4d77647dbd2f60d4df5fb297112b6"}], "guid": "bfdfe7dc352907fc980b868725387e98baebcebcff22b171a8017290b1fbf043", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98c9e4d77647dbd2f60d4df5fb297112b6", "name": "nanopb-nanopb_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e980062393f91a1d2d94e3e5ed3a5aa5da9", "name": "nanopb", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98edeb236a6bea2a184984d344e4936f7f", "name": "nanopb.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}