{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98b0bee66534de4a34b9a6ccc09f3335a7", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseMessaging", "PRODUCT_NAME": "FirebaseMessaging", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9807bb3ec7c4688c0f750d2abf286daeab", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e985a5422afc9b9ee20ab3fa317f19bf85c", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging.modulemap", "PRODUCT_MODULE_NAME": "FirebaseMessaging", "PRODUCT_NAME": "FirebaseMessaging", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e984d5a428d4bba1228b39288c9abf633e2", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e985a5422afc9b9ee20ab3fa317f19bf85c", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging.modulemap", "PRODUCT_MODULE_NAME": "FirebaseMessaging", "PRODUCT_NAME": "FirebaseMessaging", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98ed827c13a73ecc4cc9e951ee59a38747", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e985f81af140e6b7c6f737d23c6e0c10f0d", "guid": "bfdfe7dc352907fc980b868725387e98f5d74d15ee342fa61a53cf1e2f61881a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989d811001e8cd42439cc810f47f93b87b", "guid": "bfdfe7dc352907fc980b868725387e9861c1431aa7ec417cbe3e45d872cb87c0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9808dc79d56799372ebb65a4476300c96b", "guid": "bfdfe7dc352907fc980b868725387e980b7cf3090b9a139cc4bb3b254212499e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9882654ba8d541a27493cbcc211a1f240f", "guid": "bfdfe7dc352907fc980b868725387e9856c707b6e17cf0f9ccf38eea51cc6ea7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c6f078fc37a7cc104f568739cb90d124", "guid": "bfdfe7dc352907fc980b868725387e988244d559d894f70d3683f1a5c92707d3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980f62214374dde4ed04fe8e2799477c59", "guid": "bfdfe7dc352907fc980b868725387e9804a5a931c18daef923bb6d14e57e5d8f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ca65cfe680682fe4cf410e0c64e37faf", "guid": "bfdfe7dc352907fc980b868725387e9809ded183014a101e3ffd1e45199c74b8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982e86893b1b0d53ec40a4673bde2f7b9a", "guid": "bfdfe7dc352907fc980b868725387e98d4c44a1934b1f4e98b6c073f0777fddf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c8bfcda84746c3ebc0e7ef074f0e3fbf", "guid": "bfdfe7dc352907fc980b868725387e9822fe6d4dceaec32cf7b1a330164fc3b8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d47abf13cb9d651585793ce544cd523b", "guid": "bfdfe7dc352907fc980b868725387e9801bd8646aef70ec972a2c2e09f86691a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d57e38156d478df46fd345c558a12116", "guid": "bfdfe7dc352907fc980b868725387e9899cfdcc6457e5aa87d328fda7bbcc549", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9819b276596a04829a670bc81da044e93c", "guid": "bfdfe7dc352907fc980b868725387e9832a6da2bad9a03a457ebe0377bd53cc3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982eb1874c7b96a840b5b2c18159f28aeb", "guid": "bfdfe7dc352907fc980b868725387e982584b2723620b0e9a769b976c2645a85"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c029827a3d515bd70a2efbd59ff00da1", "guid": "bfdfe7dc352907fc980b868725387e985ade03e0c06823a790415cf4b789c956"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9823bae7e94cb8582f9c0d31efda2766b8", "guid": "bfdfe7dc352907fc980b868725387e9839ec2a57d4df8c8cfff52700aedc4170"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987f048348ceac149049e71a199d2a37e4", "guid": "bfdfe7dc352907fc980b868725387e98d99ba61f6e87f403972edd9d86c188f8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e77d8a760efa4df92e2b6d474601b319", "guid": "bfdfe7dc352907fc980b868725387e981933d61946c9f40070f27e96882b25ff", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eb6a5ccf2efaf360e6ee4d5159e8c156", "guid": "bfdfe7dc352907fc980b868725387e9824119fb99becb69a640185787b2bbee3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98481e7be579c179e1c4d03118316598bd", "guid": "bfdfe7dc352907fc980b868725387e98797a6b4804600505bc2eba914e024f4b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bc593873b928b6a8adf05e4b88a4622a", "guid": "bfdfe7dc352907fc980b868725387e98341f2ef5f2efd779d07cd900403c3d10"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985c0a9075e44e36130e4dc240c467d308", "guid": "bfdfe7dc352907fc980b868725387e9889d81bb59ad2b1ad3a4d7e912feded32"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986ff3626a77e1d17ff5e311e2825cc267", "guid": "bfdfe7dc352907fc980b868725387e985c87dcf83d83ef8a3225e85d9f456274"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a46d42bf6bda85ce7afe115f966a0993", "guid": "bfdfe7dc352907fc980b868725387e9874366bdbf0f972291d16f764e9673973"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9823212c63d3d5d59d199544e82f404a14", "guid": "bfdfe7dc352907fc980b868725387e986fd0624ffac399f9439dc87669660f12"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988b5959d73c7fdf22f78b5a3c7c69fe95", "guid": "bfdfe7dc352907fc980b868725387e9818387ed25ced8094feb38f2699203b5e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98df2139fd53d70902d8f649c428e51d54", "guid": "bfdfe7dc352907fc980b868725387e9835553ade55abb5cc906226bbc4f3d3b6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fa353fcce7d821928d34fdc33091d36d", "guid": "bfdfe7dc352907fc980b868725387e98e7c9442b55222d802431f96d2c97fd90"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a7bebdb76994f144d602ea6d8f89d8d6", "guid": "bfdfe7dc352907fc980b868725387e98cee78dda4b3a988632f97985276debdd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9879235e16e1b46596e5c7e77c0f7eae7c", "guid": "bfdfe7dc352907fc980b868725387e98937c036c87e416c90347938a58d5fe15"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e2cc0bfceb7b90edb70c575c502ef475", "guid": "bfdfe7dc352907fc980b868725387e98aa9e81da6d7d5d3de712f8f7381838ad"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985a3193d96e603fde5a707241d772e5c4", "guid": "bfdfe7dc352907fc980b868725387e9846a027d85d3532c2434e92e122c0e134", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9834a5024dfe1386bd05b05edfd925e7be", "guid": "bfdfe7dc352907fc980b868725387e98c10a192c5b0766cf18dccc1189912af5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f7c0fccf7aae7663433afb655954e468", "guid": "bfdfe7dc352907fc980b868725387e98e328844b4cf59c14d42bada31504eab4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9802134f216bc32d09f38a7948b5e255fa", "guid": "bfdfe7dc352907fc980b868725387e986e07e0df22ddde4096f64558e01fded4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98614591e8a525b48ff38c607d58b14ca3", "guid": "bfdfe7dc352907fc980b868725387e98a366e17e6bbc5e2417671fb04e773425"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98933d44c52ce9cbeb0a9ddd518bc2182b", "guid": "bfdfe7dc352907fc980b868725387e98cbe235391b4de200f3d6890a128b11bf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980ea10bde760a0375921f01a4aa0ae1dd", "guid": "bfdfe7dc352907fc980b868725387e982f639d9773d7f91958fdefc69aad26d3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e2b1dbad93110c0e6ea2c0f4747f3bec", "guid": "bfdfe7dc352907fc980b868725387e98dc0c64ac3b56414d536054c7ac963a63"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983c14ce8ab4f5541e625f8add11ad12f5", "guid": "bfdfe7dc352907fc980b868725387e980f7a11384cfc3b763869c2040c9026a5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989cfe804f65431a4b53cd3bc5db93de49", "guid": "bfdfe7dc352907fc980b868725387e986053bf9423979ea5e8ca064f409c32b3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9865d6b47f69b02e5db23ee436b0b15454", "guid": "bfdfe7dc352907fc980b868725387e98698f76021e33bac1a93bac7a1a5cb3e9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988398d7dfe518f71e664acdabbb2acb18", "guid": "bfdfe7dc352907fc980b868725387e98b57de04938bfcb8005437b3440915c8e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98728bf3d0053ef1dede35c2b538ce8684", "guid": "bfdfe7dc352907fc980b868725387e98f83660e0b8101feabb2d756ba6c99bdc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cc6ac774849aef4de8f8bbe2cf1b177f", "guid": "bfdfe7dc352907fc980b868725387e98c6b363bba4f8eeba44ca00b8febe4ad5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ce89e85698dc1d42aba2d36d20064711", "guid": "bfdfe7dc352907fc980b868725387e98d1add30da5eadadeae145e9d831ea1fe"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a923b00a4f012848e5712a4caa926e8f", "guid": "bfdfe7dc352907fc980b868725387e98fbb0b98a108ae8a4bb9f4adca7cfd877"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b42bb11f56639703533b23b544c1bfbf", "guid": "bfdfe7dc352907fc980b868725387e9884c3b6f0d5168fd3e7ae8bc860e122ab"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985a2d0efcaccbb7b17c5d5641e8160c00", "guid": "bfdfe7dc352907fc980b868725387e989cfd3eb1e076d23228cee87b0878f38d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bb566282e0db6b48aa265fced31b44cc", "guid": "bfdfe7dc352907fc980b868725387e982faa468b5416d638e608dd002d391a04"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d14b0b482eef229940557e8f248cb613", "guid": "bfdfe7dc352907fc980b868725387e98582b450a89372e06958026055b827d78"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98310c1c050d8397ee6ec30f156280724a", "guid": "bfdfe7dc352907fc980b868725387e98d457a2a9f9e3afdbab84871ff019c5ed"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98be0d418c7a8124a123a4ebba576ea199", "guid": "bfdfe7dc352907fc980b868725387e98beb98b266474d47daa5cba29ed175cbc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d1c858a63b48e3804a878608db3b2c08", "guid": "bfdfe7dc352907fc980b868725387e980dc74f47d743b714d6653f0cf8055cd8"}], "guid": "bfdfe7dc352907fc980b868725387e98e8abc3258befe0b2a6008e696ded1e95", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9830019ea1a775881180ffb2cb352275cf", "guid": "bfdfe7dc352907fc980b868725387e98f8afd566ba033f9ec7ad98aeeb2f0ec6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986676a2cce726044c5a6a2ac2f3289bd1", "guid": "bfdfe7dc352907fc980b868725387e98311f2461f6f4ef20938685bb03eb25a8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984422983630ca008f064cdcfc9c22a9c1", "guid": "bfdfe7dc352907fc980b868725387e98a25c3365764c68ad442dc78baea4d831"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c1202a2e482fe1c8491cbffae91a9bb1", "guid": "bfdfe7dc352907fc980b868725387e98f35e22d33f7d671327464db769be4f7f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9801aefc775b57891b94243c49f901e4c0", "guid": "bfdfe7dc352907fc980b868725387e982369191bf0621bdce0fbf2ac93f6f7c9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b1d80a4dc0896b0ec90c1ae61cdf8ae3", "guid": "bfdfe7dc352907fc980b868725387e9875d71d549d7a433d7ac07479a18f4b5a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980ce23cab21900546ded8c0ea8cba8348", "guid": "bfdfe7dc352907fc980b868725387e98a07db68c88b8a9e08932341c460ff5c3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986fd9fe7095e4d68f1bef4e978026f420", "guid": "bfdfe7dc352907fc980b868725387e98c0d4b5a2c47b6a4ab52b0c33cef51308"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98359b74c01fc38350686f4978a1d27cb3", "guid": "bfdfe7dc352907fc980b868725387e98469abc956a48601c040f9ecbd2b0737a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ef01bb7bf747f2a88a20a19805d3be1c", "guid": "bfdfe7dc352907fc980b868725387e989f5b75a0e10e93d72c18553a73729293"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982066e7496b6f2a86a958d91f5b4fd4df", "guid": "bfdfe7dc352907fc980b868725387e98d2af4fea501d1508528603842069a84d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d90d09638f8b9185c7d4bb5a238bca1e", "guid": "bfdfe7dc352907fc980b868725387e989ac185da538dde41436d55b80d09bea7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e49cb5ca1b434805e16d3247fabc18fe", "guid": "bfdfe7dc352907fc980b868725387e98c5dac6376e398047adc4f485c4631fe2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98854ea6ef64fc867c0707e9fdfe5ac845", "guid": "bfdfe7dc352907fc980b868725387e9840fdd417714c243e875532e2b7d95a10"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985b743c2592888dc3196b4f20f54b19d2", "guid": "bfdfe7dc352907fc980b868725387e987d2aece9de640922ff026c95b14adbf3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988e853ed870d5d16f0484e818be97981e", "guid": "bfdfe7dc352907fc980b868725387e982781ef17bff97a222286bab67c1f9949"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982415d6b1765155f798afb1b5fcda8163", "guid": "bfdfe7dc352907fc980b868725387e9819a62efb25430ccfffacca10d9ed9496"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983676fa48b0fb69181714225a8899d772", "guid": "bfdfe7dc352907fc980b868725387e9810b1b7708c0da4e42250e898614dff60"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9813aed81ce0f84037621cc43b8cb01d43", "guid": "bfdfe7dc352907fc980b868725387e98003e2d503961a1f99b5723308560d114"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989181ce8cc78b68ec95946cabd5d024fb", "guid": "bfdfe7dc352907fc980b868725387e983ac6aed160b42f80bad8b270e18895f1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986f59f238bcfb6e413c362bb4efb0adb7", "guid": "bfdfe7dc352907fc980b868725387e98f08c59c854bd89244e83e7213192ef0c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9891d3be4d6c48a4725a5068ad450e506b", "guid": "bfdfe7dc352907fc980b868725387e9809a94e929ba7a5f38beee9ec9955d66b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9862008fe61989c65d6fdf61675008f0ae", "guid": "bfdfe7dc352907fc980b868725387e986d016ac3710d98b47c86cf0c570b28dd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9801c56d3cd68c4dafe9e879efb319c790", "guid": "bfdfe7dc352907fc980b868725387e98329d669d6900a51e316d7f66faab367f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98be9bc6fe68176070ee5e1dc4d1977f55", "guid": "bfdfe7dc352907fc980b868725387e983bb82c18b31f8642159c404bb85cf816"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983cf3dcde1da20459c723ed85a949ff04", "guid": "bfdfe7dc352907fc980b868725387e98f1b0e092f76416f261737886835fcacd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98010281e702bab2ea89f3befd765c3eb3", "guid": "bfdfe7dc352907fc980b868725387e9837ee2140be872d4a3d4974ce618055f1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98014f21d20f327d53ad911bf0ba969255", "guid": "bfdfe7dc352907fc980b868725387e98a225312c7443f1709b587177da28bdf7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984f3d34b9808370d79bc7ba19701e456b", "guid": "bfdfe7dc352907fc980b868725387e98786accbddec3e9f96844aa6c681214f3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cd8e22c6e6fb4b27efda268e9430d01d", "guid": "bfdfe7dc352907fc980b868725387e98a9b4f55961ba517f0217b9c0a9fd9630"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9811f69a653423868caf4f5e8248393897", "guid": "bfdfe7dc352907fc980b868725387e98fe80b940316c55dadec14d020b08da1d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9854ac59469bd58c6b6ae2a59a7aaf24ce", "guid": "bfdfe7dc352907fc980b868725387e98057ec17c87cf9b7f4bd670e413aa9362"}], "guid": "bfdfe7dc352907fc980b868725387e986ef8ad79c2776b7febd9e9383836cb3c", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9823eca4b3b33af74b846381c4acc45081", "guid": "bfdfe7dc352907fc980b868725387e987b29e6ca96afd9df84b7425650294353"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984a8cea545494ea5a0e236a9019870cf4", "guid": "bfdfe7dc352907fc980b868725387e98684d0afbf455281c5c0388fabf0a9c5b"}], "guid": "bfdfe7dc352907fc980b868725387e9864830586489d3d86a16587c439e0b7c0", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98fe4428a2434ca82f45b84386f77974c5", "targetReference": "bfdfe7dc352907fc980b868725387e98974c3b2447afb83a5c25c38d101a48ab"}], "guid": "bfdfe7dc352907fc980b868725387e9806afc2a4b553d9198843f7e0b77bc120", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e98566ec9a1d71c4629f4f85ecb735ce614", "name": "FirebaseInstallations"}, {"guid": "bfdfe7dc352907fc980b868725387e98974c3b2447afb83a5c25c38d101a48ab", "name": "FirebaseMessaging-FirebaseMessaging_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98d3c8dfff2c580c352f83d3850ad17775", "name": "GoogleDataTransport"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}, {"guid": "bfdfe7dc352907fc980b868725387e980062393f91a1d2d94e3e5ed3a5aa5da9", "name": "nanopb"}], "guid": "bfdfe7dc352907fc980b868725387e983da17a3564c774dfaa331fa07754d2bc", "name": "FirebaseMessaging", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98b3b0fadaedeb0138a07668440d83e3b3", "name": "FirebaseMessaging.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}