{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e980303259c6efc23458970c8ba17c70653", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/.shorebird/bin/cache/flutter/0ac69de92c7201830d6a687a44fa0d34f6003f90/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/.shorebird/bin/cache/flutter/0ac69de92c7201830d6a687a44fa0d34f6003f90/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "flutter_image_compress_common", "PRODUCT_NAME": "flutter_image_compress_common", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e980fe6008bd0ea7d60a239f5cfb496f9fd", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98297fc411a10247da705f7806b3288fc1", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/.shorebird/bin/cache/flutter/0ac69de92c7201830d6a687a44fa0d34f6003f90/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/.shorebird/bin/cache/flutter/0ac69de92c7201830d6a687a44fa0d34f6003f90/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "flutter_image_compress_common", "PRODUCT_NAME": "flutter_image_compress_common", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e984ac27f284fbf5fa4242b11e4c252631e", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98297fc411a10247da705f7806b3288fc1", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/.shorebird/bin/cache/flutter/0ac69de92c7201830d6a687a44fa0d34f6003f90/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/.shorebird/bin/cache/flutter/0ac69de92c7201830d6a687a44fa0d34f6003f90/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "flutter_image_compress_common", "PRODUCT_NAME": "flutter_image_compress_common", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98ec29c327167a123c1352766ac15557e9", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98477410d388113c5b3a60856d6fdeb94e", "guid": "bfdfe7dc352907fc980b868725387e9817011278146d850ad8c1800732eb1e38", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9886eab04f60e787ac1fecc87a98054b05", "guid": "bfdfe7dc352907fc980b868725387e980ac0e33a793cc19ff4abfb357eae768f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a385828193c5a6c1ad65dc9e928bd897", "guid": "bfdfe7dc352907fc980b868725387e989d6f395bb26cbecbfd60c1405ceff351", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98be53c3971a765723633b9db1cc0e9e69", "guid": "bfdfe7dc352907fc980b868725387e98df995503faf86a7e536e8fd0813b9b8f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98787b2df919a5b918032526575dfb5add", "guid": "bfdfe7dc352907fc980b868725387e984a42ac0fe50661fb4253f6986ea45bd3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9804cd4fa37d3dd679cdb60c034880045c", "guid": "bfdfe7dc352907fc980b868725387e98de0db65b473b31d0081f4a5269702284", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986468c41e60e3ed651544c3d8d7cd8a12", "guid": "bfdfe7dc352907fc980b868725387e9860e7129d76385a521f0b9fd136393b10", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982b8e97303f65caf3f6f0d5ede44c4eec", "guid": "bfdfe7dc352907fc980b868725387e98c102fd841861eee3b0b8bacef085b917", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98da13af53225d0af7c9002d74d4dcae8a", "guid": "bfdfe7dc352907fc980b868725387e98f8633a22aa08547c812ac9bec8b70ea8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9877bbb37108dd3f5715c9432ca0d879c6", "guid": "bfdfe7dc352907fc980b868725387e98daf47c85e530f3405587d1d40166baa5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983561aac62228cdae201395841d8171fe", "guid": "bfdfe7dc352907fc980b868725387e98acadf1494a3e3cd73f3caf0731d75f80", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984b99820fc23848d210555033b32a41a8", "guid": "bfdfe7dc352907fc980b868725387e98cdf6a5c0e94262f9f4ac605bffef3bfb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9855e8c2e779e1bcd7ff31e42f9cf5d292", "guid": "bfdfe7dc352907fc980b868725387e98749c0e50cc6ab0c5d2ae99062d78bd10", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98058b48718eb24ef4f6d74adceca2095c", "guid": "bfdfe7dc352907fc980b868725387e98b60c9e9460d623e196c3542325e313b6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985f5ed8194846859cf149dfceb6a3e169", "guid": "bfdfe7dc352907fc980b868725387e984176d1afb1f259dfdc07efe17ea652cf", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fc0e651f45b10f60d637b1d952113e4f", "guid": "bfdfe7dc352907fc980b868725387e98bc8f2f12d75589e115d9449c5986f9fd", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98efebc628f5d4aac7399914dd4f2b0c90", "guid": "bfdfe7dc352907fc980b868725387e9818b5f752d3526a5d027411efb16100bd", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f6619473e4b419520ff024aeeb10fb42", "guid": "bfdfe7dc352907fc980b868725387e989de49e9d16c76d6cdb2cd05defe828b7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eafb93bb8d2a8b3495042ec34c94a932", "guid": "bfdfe7dc352907fc980b868725387e980869c8041a3db4283bedf358c81f931e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eae67407e69046f0c00e79fb474aa85f", "guid": "bfdfe7dc352907fc980b868725387e98c1f1e163902f92d3f444c8eef23fe9e4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986707d29835465bdffa8f85297c5cf030", "guid": "bfdfe7dc352907fc980b868725387e98ae14d59b03d674cb5e1bad0f3d076dd9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9869da45ac54ba377104835cf27f11a53f", "guid": "bfdfe7dc352907fc980b868725387e98edc2a89fb1a6e200ab1412ec87f9c9aa", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ef0599a0905485f4ce05d3d4b41c8efd", "guid": "bfdfe7dc352907fc980b868725387e98492b5866dd16c57c285b5e79c6867b49", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98281ee2f64b10ece8c6f51a715f80d8a7", "guid": "bfdfe7dc352907fc980b868725387e98e484c4f5150fbf8fabdad69f35a52c75", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9895f1fe5e7f89a57dfcce192dfa441b11", "guid": "bfdfe7dc352907fc980b868725387e98d15e2dd2c99fea1c18b18e69ffd1b703", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985ba3eae97c70e7af86ea2ed1d99d67bf", "guid": "bfdfe7dc352907fc980b868725387e986fee5e9c3343f8d1008352e701bc900e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b66df8b8221d01cc9ebbb5de321cda8b", "guid": "bfdfe7dc352907fc980b868725387e98a3b33d70365c2c21c6e5ce5c403a6767", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9873ca16895edfe41d008e852687d4e82b", "guid": "bfdfe7dc352907fc980b868725387e98bc874e43a4cb78d8f4d1a0ac40c942bc", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98934cef9bf6ee848bfe3ec38d62197835", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e982b2603b87ef9594c5b54a862e3ca7656", "guid": "bfdfe7dc352907fc980b868725387e9829d2b0b2b7d95a9ddf7e598d6ffc4db1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988fe3a9b3f453dfdfb0cb51f1c15a2aac", "guid": "bfdfe7dc352907fc980b868725387e988ad79f08695ea6c2cca3c5d7d8b8e33b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98daf38c2077c98f70457a746a98604f16", "guid": "bfdfe7dc352907fc980b868725387e98eb8f6a719f355b17f23d8eb2e9a5d44d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e0afd4187c2db8fc6f27652a4abe833b", "guid": "bfdfe7dc352907fc980b868725387e9816670788bc7f1e372bfade4c15b03b8d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987e26449b890adab6a221520d81f49f0d", "guid": "bfdfe7dc352907fc980b868725387e98838df245c5366c7ccd43779dddceac8f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cc00b1ddcb70d91f0c0612737007396e", "guid": "bfdfe7dc352907fc980b868725387e980cc73214077d885d0ddd9bbb802b0c2c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986e2b53b0ae169b692bb58ee99d1b80f8", "guid": "bfdfe7dc352907fc980b868725387e987699d61d47db16d864283d0af14a2a38"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98097025f49c2e5a77405d5fa59e6944e2", "guid": "bfdfe7dc352907fc980b868725387e98e97804a71a7eda4782f6d18bbb55dbdd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cfab547d675f8e196617ac3555817e01", "guid": "bfdfe7dc352907fc980b868725387e98d47beba43c550f9d5c74eaccd5ff08b8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988e53b7debe8056ca450914fc80ebceb5", "guid": "bfdfe7dc352907fc980b868725387e982130a66579435704115f7e00d09f53d2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983178bc83cf6e3ab74cad003719e1b4f7", "guid": "bfdfe7dc352907fc980b868725387e98163ea20656902ae2aebec0d2b7d2c959"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c7c3d96844f6c4594a195b003c5698d0", "guid": "bfdfe7dc352907fc980b868725387e98c065af2abb2799503e76158497948974"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982a5641ed5453c111028ef150dfc74045", "guid": "bfdfe7dc352907fc980b868725387e986c074fcd043656e86edc370b80f2b6ee"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98df17c1df4825ab8554fac065a1373b03", "guid": "bfdfe7dc352907fc980b868725387e98897022fb7c83452617bb5421eee661b3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f22b5a50ebf67fe3f641ab501f96ceaf", "guid": "bfdfe7dc352907fc980b868725387e98cfe537ef6fd48aebe0e7b866f6b1142d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98539a78002914fc3f6b21af2ba23fc635", "guid": "bfdfe7dc352907fc980b868725387e988830eb10a635306371e79be09704e0ad"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982fa8d951726cf106ea722d7bc1d257b9", "guid": "bfdfe7dc352907fc980b868725387e989ac43435114fcf14579f257bc3f9c364"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a4fd47e4a74977b053e22a0a7de698be", "guid": "bfdfe7dc352907fc980b868725387e985cf8026ab77fe57445a9e11bc4c4c7ce"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980d56e4574111882fae188992ba2c1203", "guid": "bfdfe7dc352907fc980b868725387e98ef071ab9cef1d242de5b1113119d33b5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987d38b20e0968cf2a4d0420a61c7ea91c", "guid": "bfdfe7dc352907fc980b868725387e9852ddaf4c3962f25802ecf6e4d216c596"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9880a93e03ef71c28f572c2f2f7416e067", "guid": "bfdfe7dc352907fc980b868725387e98abfa7315b06c10451197abd58e79d711"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9837ff93c578fe579c6af073fe9508ddf2", "guid": "bfdfe7dc352907fc980b868725387e987f00ce95bbef4bcae9c2b6d4ab613e85"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987893b70d65db068932be85255afbb257", "guid": "bfdfe7dc352907fc980b868725387e981243c2de66d0689b6cc84852675e392f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e5e10a77cbf5d4afca294ebecb1625a8", "guid": "bfdfe7dc352907fc980b868725387e98a7f2e7eb44ba9c0b257d1bf51c2035a4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98287d4d0265fe261bfb962c32c607fa05", "guid": "bfdfe7dc352907fc980b868725387e987e1026d7e32253d02e2a13c87719a604"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98df55812330d2ced05554ec2931f8ab3a", "guid": "bfdfe7dc352907fc980b868725387e98fe57562c583a8a215f5783496b1ef801"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fdb83465cc6a749586c2eec2df3ffac8", "guid": "bfdfe7dc352907fc980b868725387e98aa36efef915c33b7edff59f6a9a0bbfb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98246acd1a4a0bd267c2bc7f6004cdf4f1", "guid": "bfdfe7dc352907fc980b868725387e98fd0b1420ef9ecbffd05e08127fc813c3"}], "guid": "bfdfe7dc352907fc980b868725387e981f18c3bdd543f0383a05196ffefabc9e", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9823eca4b3b33af74b846381c4acc45081", "guid": "bfdfe7dc352907fc980b868725387e9808188f8afd5c22436d8f270ee53ec170"}], "guid": "bfdfe7dc352907fc980b868725387e98f59cae5e753b0a31876694ce85c47944", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98ec476f90f1f6e616e836f5cbf5a8826f", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e982742fb9f3ddc293e43c65bab714408c6", "name": "Mantle"}, {"guid": "bfdfe7dc352907fc980b868725387e98c46180aea4e87057640961e6db37df0d", "name": "SDWebImage"}, {"guid": "bfdfe7dc352907fc980b868725387e9837027570fe3a09bb9c7d0d2620332306", "name": "SDWebImageWebPCoder"}], "guid": "bfdfe7dc352907fc980b868725387e982ec175b6b4d6149d1cce89f5f0b3694a", "name": "flutter_image_compress_common", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98b928db338a2b0bf59a36f34e391aa676", "name": "flutter_image_compress_common.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}