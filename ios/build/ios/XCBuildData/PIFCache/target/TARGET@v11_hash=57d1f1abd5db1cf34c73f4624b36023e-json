{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98a974c566df090578e5becad0b16ded82", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseCoreInternal", "PRODUCT_NAME": "FirebaseCoreInternal", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9845adf71458895c727aa0676f03e6af13", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98ab7705743e5195cc8a7b4793f376fe7c", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCoreInternal", "PRODUCT_NAME": "FirebaseCoreInternal", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9850850dc2054b88614db6f44f69486524", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98ab7705743e5195cc8a7b4793f376fe7c", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCoreInternal", "PRODUCT_NAME": "FirebaseCoreInternal", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98e9be1bfe65eb1b300780449cff8d624b", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98c451304512f4da23dbabafaea51886b2", "guid": "bfdfe7dc352907fc980b868725387e98e9f1050ab7510d82688ae953ba859095", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98d2e2d110eac01d8471f1fc2fcf1f58a0", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98238ec595beb2047926c8d8bff6cc1374", "guid": "bfdfe7dc352907fc980b868725387e984e9570794d288befb98ed0dc0b859ef3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98282d17fe45ac5d8b83870085975cd204", "guid": "bfdfe7dc352907fc980b868725387e9858e0240cef8af3ab48a0fd3edf28ad87"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b83036ae2191df7929e690598ff2233b", "guid": "bfdfe7dc352907fc980b868725387e98ab08dbf9dd7ab06b716f117aa0dd61f5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e8f1ceffd29120491b2f746ca0df3687", "guid": "bfdfe7dc352907fc980b868725387e9816581d3b78ca91680d7753a8e4eab4ad"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9849fd6eb647557e225be1f64141a36900", "guid": "bfdfe7dc352907fc980b868725387e98900567de568466e0dada40f81553683a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983a7b49307ee1ace24c5ab3281da546dc", "guid": "bfdfe7dc352907fc980b868725387e980e9104c0d0855f1532ee031f9bc77f34"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d865d883214d410721d275cf0a69e7b8", "guid": "bfdfe7dc352907fc980b868725387e98571b2f345ac72e53eac900872e80e54d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980ff762f490a88c977d895b8180cab908", "guid": "bfdfe7dc352907fc980b868725387e98929a5baebf5804bbe8c8c3c46224a571"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982743bbbfed68482667bddac6117bdea8", "guid": "bfdfe7dc352907fc980b868725387e988408888b9135a6b5b1209019280d0811"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9862ae909acf3c002eed8d089c0c3e2645", "guid": "bfdfe7dc352907fc980b868725387e98ed13ffe3bea83c3f40cca99d08d60a48"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98beb824bda2502e371e7d7cd7493eb2cd", "guid": "bfdfe7dc352907fc980b868725387e9891109a656770e6b34b991fe16d86f8b2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9814eaa39b71f20e613da1a1b95a420f2b", "guid": "bfdfe7dc352907fc980b868725387e98fb96177c68d8882332b37e5c029bf9af"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9884b08d30bdf5efaa468f82b7c1abf4b5", "guid": "bfdfe7dc352907fc980b868725387e98756ffe02ada559c24df2818d0907c5d4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981059681188f70dfbe4b2deffa1765f3c", "guid": "bfdfe7dc352907fc980b868725387e98a1688bdbf79bd7690594328b5c0f7c67"}], "guid": "bfdfe7dc352907fc980b868725387e98e06c97993d869f23ed996713fa79cea2", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9823eca4b3b33af74b846381c4acc45081", "guid": "bfdfe7dc352907fc980b868725387e987e58495fc27a1685b209cb13d919324e"}], "guid": "bfdfe7dc352907fc980b868725387e9846a6ef133975295aace677a0a9f2da80", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e9866b8456d53943b1c9d1f073522622451", "targetReference": "bfdfe7dc352907fc980b868725387e98e5b592b076e092ab7ac9d9b5c85edc6f"}], "guid": "bfdfe7dc352907fc980b868725387e98118154465fc27ee122998dd169ba9f0d", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98e5b592b076e092ab7ac9d9b5c85edc6f", "name": "FirebaseCoreInternal-FirebaseCoreInternal_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}], "guid": "bfdfe7dc352907fc980b868725387e98020791fd2e7b7ddc8fb2658339c42e16", "name": "FirebaseCoreInternal", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e983d86e87924acfad2934921ce7ad9fbea", "name": "FirebaseCoreInternal.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}