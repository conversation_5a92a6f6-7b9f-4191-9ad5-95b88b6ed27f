{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9846c4f9061a094a67c5a3f99c5f810c15", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98544a37d4f280c7e98386b25889285089", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98865d4ca49a272748d323209840836842", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98f867d906489c88142d2bda697d328902", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98865d4ca49a272748d323209840836842", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e984675d16d5ade7cca85aed65033bf364e", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9825c691862860fa5759ee589a409999bb", "guid": "bfdfe7dc352907fc980b868725387e9824b8a5226ed557d0a86fefdcacfbee47", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983012cd1deab22d87e9e6c50c8cc7badd", "guid": "bfdfe7dc352907fc980b868725387e98dd2c2487e4d289620e43a0fcf36a11f2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b90ceb513fd28dd7a1e76597a530b384", "guid": "bfdfe7dc352907fc980b868725387e9852938c336dd5c7717b9944aec2c9cff0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987d6e4882ac348873e9f3ed3cbbbea19b", "guid": "bfdfe7dc352907fc980b868725387e98717a55de8f0970bea3cee342ef22a47b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98db85e9053764b2c371f77281b08afa48", "guid": "bfdfe7dc352907fc980b868725387e98377651edff163ee7d8164504e4f02a9e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982f7fcf58e1a3a3a99972a11ea4359004", "guid": "bfdfe7dc352907fc980b868725387e98b966fb8e797c5e34d6fcfeaa5efb0100", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f52c36cfb3ce4f4eb6a55d9409b13ab4", "guid": "bfdfe7dc352907fc980b868725387e98d0b76973420403b8a2c068c1d67fa28a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980f15ac42dfe49ead1ea6708495086f4b", "guid": "bfdfe7dc352907fc980b868725387e98ba72cd79dc53dc1dcc20b6e2290b9152", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f6ff67d09e74ca853fdb13e0aefd65b8", "guid": "bfdfe7dc352907fc980b868725387e98175d7fab8d46c7e826c07b065a2ccc8d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982048e7f509c6702e4d836e74e37d01f3", "guid": "bfdfe7dc352907fc980b868725387e98c58f2daec14085cf53d881d3ab962291", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98044a3d4d536918060cc15c89f437c5db", "guid": "bfdfe7dc352907fc980b868725387e98d1ddf18f3c01dfdfd58fe28aa29488b3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982a22dc40f652b2759c0b21a9e84ab342", "guid": "bfdfe7dc352907fc980b868725387e9874e07406f1d0691164a010cf86ccb4aa", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b74aa567627255528a98e1139d20cc1e", "guid": "bfdfe7dc352907fc980b868725387e98c92d80f0b159ecd729100dc86cbda7a3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985e4035c6e4ff8441ceb2620be4afb873", "guid": "bfdfe7dc352907fc980b868725387e98fb7c0e1192088f4594933b1222a225d4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983c90f5af917103c451866fa6016f478d", "guid": "bfdfe7dc352907fc980b868725387e98509f5f420ee1fc9d51be1dee7f3015c9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98399c82975a863d589f74e24c1f7b75c9", "guid": "bfdfe7dc352907fc980b868725387e98b70339fa5b451d1b768de0f846f2b056", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9853a31d7a69bca027ebe5d97c3f9544d8", "guid": "bfdfe7dc352907fc980b868725387e98808d523db0b23806d5cd3daa1948d161", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98df1b2c0a951b1d14de74077f7ead113b", "guid": "bfdfe7dc352907fc980b868725387e98499251487bdcd1cfd7761b67264a62ef", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f589f101c79137636d0f7845d866b841", "guid": "bfdfe7dc352907fc980b868725387e98f3afcc8d6339e019c2dbd258f3d3dfc2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984072f1f6182204334e8ddd8b88207160", "guid": "bfdfe7dc352907fc980b868725387e9880766f64b4bee255c84645fee3c2c665", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9859a4178ee0e9b4c09469759d6a22fe99", "guid": "bfdfe7dc352907fc980b868725387e988de0fef806513db42e9d5520058b4b0c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982ea7a49134742905c825fed9162be9a8", "guid": "bfdfe7dc352907fc980b868725387e98c527a0baeedc55077d6d2060e546edd4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987fe3f7e7b593a8fcf6e8e576bb608e22", "guid": "bfdfe7dc352907fc980b868725387e9837e05199d5678f60f65ef8a89d575d0b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983078ed3bd88e906a869ef6bfe7378599", "guid": "bfdfe7dc352907fc980b868725387e9856d0261b29f7921c104bc638e8b9e1c7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98402dd2942fc8fd954d184fb33238fee5", "guid": "bfdfe7dc352907fc980b868725387e98cf1e349da4f7905be203f21ae590cddd", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985129cb82abc3580455d90298751712c7", "guid": "bfdfe7dc352907fc980b868725387e98b175a189025e7659f0196c02f1f6df6e"}], "guid": "bfdfe7dc352907fc980b868725387e98c6260971136fcdf961b9faa5f82ccea2", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9896d57712ddf76a803bd2a476480b2446", "guid": "bfdfe7dc352907fc980b868725387e987409a4a44321c4242fc5ffc5ab8d1ffd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988f91a8372b32099b74eb75bc86e631b8", "guid": "bfdfe7dc352907fc980b868725387e9851693715bb69d8fc9dfd9137c1b29e70"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98434574e404ff659713d1be5fc484cf77", "guid": "bfdfe7dc352907fc980b868725387e9865586b6ca60b3db56b8c7ad53e8c7e4b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9878dfe3a4b515bb6318818265a2df954f", "guid": "bfdfe7dc352907fc980b868725387e98420eb87ab3c582230f6aafbea84d27d5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c9377077c13377c97dcd91900bb022d1", "guid": "bfdfe7dc352907fc980b868725387e98ba23747254ababfc0ccf6ba17c882c4e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988deb013377ad5f2ebe21953b1de81c17", "guid": "bfdfe7dc352907fc980b868725387e9884289086cde8c526efef8f35f8f0550f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982169d90b810784236f4264ae5f377d0a", "guid": "bfdfe7dc352907fc980b868725387e987b225f78e4cb5b221718ac025714ef2b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981e57b00dd6d03896e797eda87a1c7306", "guid": "bfdfe7dc352907fc980b868725387e98c4eabfc483ed0d3f2d6a2f363f96acb1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eee299cd0fd9fc937eeb7847a3096d98", "guid": "bfdfe7dc352907fc980b868725387e98b2053fbe77e491b925a40cd0ffabe3b4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d70fa88d3fbfc8f35cf4f69484b5c6a2", "guid": "bfdfe7dc352907fc980b868725387e9834b3fdee8fa385d8c8633b545cc5d0a6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e305f1eb857c883ebc7b687bff9be657", "guid": "bfdfe7dc352907fc980b868725387e98c57aa035136d1bdbb3f3c83990aaf9e3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dfc1d4e37eea6177490eb8bda4988100", "guid": "bfdfe7dc352907fc980b868725387e9872805d3c2099d0a60efab41d6ff0817c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e00003bf7b7807ce958e99a08937dd44", "guid": "bfdfe7dc352907fc980b868725387e98393ecf94aac5529411fd3de51372a91c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f6d73f02b644a51fe0cd668d511e4d57", "guid": "bfdfe7dc352907fc980b868725387e98a9fafc6af3cd0196afae38642956071e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98360fe68c83cab640ba7e7fdbe0618b20", "guid": "bfdfe7dc352907fc980b868725387e98b5a1898587d0e5bf473a080af8576768"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988261df603fe11800fd1be43658aac6c5", "guid": "bfdfe7dc352907fc980b868725387e98fc0407bf73d20cce88f2a7e699ab5ecb"}], "guid": "bfdfe7dc352907fc980b868725387e9848cecd7806dde4683acef9be99c211f5", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9823eca4b3b33af74b846381c4acc45081", "guid": "bfdfe7dc352907fc980b868725387e98eb537ed86834dd2459e3dfffe8cf28f2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9818e1d7dfe2849b95e08804531e42cab8", "guid": "bfdfe7dc352907fc980b868725387e98b3e532062c19ffe3e48b1ed760c10b40"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984a8cea545494ea5a0e236a9019870cf4", "guid": "bfdfe7dc352907fc980b868725387e9810271319a64cb6f15bfd6c4387bac598"}], "guid": "bfdfe7dc352907fc980b868725387e98eff0458236913c25bbba2024c5d55d9a", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98d66efdef2f9b72aaf5541c8dd9c4a1bc", "targetReference": "bfdfe7dc352907fc980b868725387e981a9fac6eb9c80f8eed49fda0531af6a4"}], "guid": "bfdfe7dc352907fc980b868725387e9899642be01c7d8208cbf0c1c1666b8641", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e981a9fac6eb9c80f8eed49fda0531af6a4", "name": "GoogleUtilities-GoogleUtilities_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98ca49ca851f2777b997a3e74ccb860358", "name": "GoogleUtilities.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}