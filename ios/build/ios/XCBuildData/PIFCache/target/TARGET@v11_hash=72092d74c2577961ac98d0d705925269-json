{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e989799704378ef630f4650db72f7de2ad0", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreExtension/FirebaseCoreExtension-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreExtension/FirebaseCoreExtension-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreExtension/FirebaseCoreExtension.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseCoreExtension", "PRODUCT_NAME": "FirebaseCoreExtension", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9855a0a2d88a81537d3abd60296106f9e3", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98cb36c731a9648a0dc12f6edccaea221d", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreExtension/FirebaseCoreExtension-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreExtension/FirebaseCoreExtension-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreExtension/FirebaseCoreExtension.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCoreExtension", "PRODUCT_NAME": "FirebaseCoreExtension", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9842c07cd536c4ec541a99b099f9ad4287", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98cb36c731a9648a0dc12f6edccaea221d", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreExtension/FirebaseCoreExtension-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreExtension/FirebaseCoreExtension-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreExtension/FirebaseCoreExtension.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCoreExtension", "PRODUCT_NAME": "FirebaseCoreExtension", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e986e9cf13779cf0b42e69865d032c4800c", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98c56d1bdab6bb8d6633afb794a062e0ab", "guid": "bfdfe7dc352907fc980b868725387e984b1a9a5dc3475d53225b7675ac0238f3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9879b15fdc7d71540b57f4a1dca4175abe", "guid": "bfdfe7dc352907fc980b868725387e9830d6ad054c64bf42461b392ff4452907", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d9de485cb95bb7c18968491da97b4e7b", "guid": "bfdfe7dc352907fc980b868725387e981cbf2cb2d45172335239a2e3d1c946a7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9831ac2e284a7f230bc0cd91ee8ec0d9a8", "guid": "bfdfe7dc352907fc980b868725387e987763c0bc10b3dfa6f1ab21af74b0028e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984c16baa77bf0a2e9accd8de5896bf5d4", "guid": "bfdfe7dc352907fc980b868725387e9858c0d4fc1499817164a825a78d066332", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984bcb2bcebcd11a98db90dfc81aa05dad", "guid": "bfdfe7dc352907fc980b868725387e984c2dc9964ae79f327674cdc9a84ff54c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a644e84c3052fc8cc1d57e6a14d3a735", "guid": "bfdfe7dc352907fc980b868725387e98de7445cca85119164f44ecdbfb327311", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98df8f7d6bb11cf445a493277cd50d9a48", "guid": "bfdfe7dc352907fc980b868725387e98f90478c4a7b95eba614c231e6cc7eff5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982d192fbe99e700aca965121ea8a19f35", "guid": "bfdfe7dc352907fc980b868725387e980ff32dd9c0f01469de5b23be18383db5", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e980092e00c2bd4691e2124a28877411e65", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e983f88607add7896137ca916c7c6a4348b", "guid": "bfdfe7dc352907fc980b868725387e9803157e9e02ac23c0e1d0e281a13f5940"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988ebc1e8e070e86bc0662113d039d655f", "guid": "bfdfe7dc352907fc980b868725387e98c3ad287d55a4fbcc91704d5e609489f9"}], "guid": "bfdfe7dc352907fc980b868725387e983083cc07e78f1451c5d68c418199aeaf", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9823eca4b3b33af74b846381c4acc45081", "guid": "bfdfe7dc352907fc980b868725387e98ff70c96f2722481c02f6b0090e271be5"}], "guid": "bfdfe7dc352907fc980b868725387e98ee5219172cb935cdd2d85bc0fc90d429", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98f6dbc527d3b3d3374b377163da020487", "targetReference": "bfdfe7dc352907fc980b868725387e98c04ead258c2ba3f656422d1784107881"}], "guid": "bfdfe7dc352907fc980b868725387e98aa51b80229f52f274df8d8ec2412c665", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e98c04ead258c2ba3f656422d1784107881", "name": "FirebaseCoreExtension-FirebaseCoreExtension_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e982fcb5e27d041e48b96b3ab14ce32d5f2", "name": "FirebaseCoreExtension", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98311e6292af5af43c801705cd189cc184", "name": "FirebaseCoreExtension.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}