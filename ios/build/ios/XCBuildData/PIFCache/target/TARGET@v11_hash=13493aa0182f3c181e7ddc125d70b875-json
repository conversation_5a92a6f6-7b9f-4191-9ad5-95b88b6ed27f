{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e989cb4e3e46b753f193e302cf2c5ffee11", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98882981621bc8a20e104d823810356e09", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98775fcaab59992054a91e2e7c7e064b5a", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e984da2e7f0a5427d9e4ce27cf3690b2174", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98775fcaab59992054a91e2e7c7e064b5a", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98047c9cd1de6877eaeb71f950fcad64dc", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98aac61af7ee6cee5d13766f9607489c2a", "guid": "bfdfe7dc352907fc980b868725387e988897558fcd0c0405327a8af8127c3fe4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985722b31fcb708e9557c864a710fd2d82", "guid": "bfdfe7dc352907fc980b868725387e98187ab75417092b1e99d9758bf149be89", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a62729f451533dcd81a131d5093d6ea7", "guid": "bfdfe7dc352907fc980b868725387e986cc53042c8002a8898fec9881d370297"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a852031e3cc1571bd3d7db1d9bef5d07", "guid": "bfdfe7dc352907fc980b868725387e98aa10dff7a699df0369a4cd134269b4df"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98650954cef6a1852edec8b0803adbb1f5", "guid": "bfdfe7dc352907fc980b868725387e986642c933b8b7c4ef364757282798dc51"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ba466e455b5833e372f0d3ebfccfb310", "guid": "bfdfe7dc352907fc980b868725387e98b99cec27e48b198d5de4170b45a77b34"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9810e99ba1e14db817cb2fe592f2993b92", "guid": "bfdfe7dc352907fc980b868725387e98592f1883d056d6e3fe1fc10f1a4a95b3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b5bae66ecaabcd53bf420897965b034f", "guid": "bfdfe7dc352907fc980b868725387e986a50f6bb6e6c7245041b8ffe38f07870"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980b004dd2d13148b08eafed362d7164f2", "guid": "bfdfe7dc352907fc980b868725387e982bf2b70a15e87e3450ed059dc8bbb5b7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d8c8f90d61327fa9bd6b171c9bb9172f", "guid": "bfdfe7dc352907fc980b868725387e982570c952e5be61e08abc3154c09c122c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986c336ef5077f840fb2b18094514c7f2d", "guid": "bfdfe7dc352907fc980b868725387e9856357a463e6118b5bdcd77248066a351", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983da0a10226bc112549c5902f112fcb57", "guid": "bfdfe7dc352907fc980b868725387e9859127f3f9aada42aeba89de5671d8448", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98170a783318af5d7b459515fd6183113d", "guid": "bfdfe7dc352907fc980b868725387e98b116f952c4eb124b7b5b612c3534c608"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f26aac71c137fdfb665a7f52f461a320", "guid": "bfdfe7dc352907fc980b868725387e984c162673cdf4bbf96c2c659dab62104d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9858f310f168c0cc05b572a9c0ee108fa2", "guid": "bfdfe7dc352907fc980b868725387e981b0dbdcfdf6cb92c2b77da5cfbbc3bec"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988e06e0245c32cb768e2dd47c387c09e8", "guid": "bfdfe7dc352907fc980b868725387e9808aad5b22c125b0879ecece26dfac24c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986220b94019dc2f6fe3ed96c16dedfc26", "guid": "bfdfe7dc352907fc980b868725387e987331ea629a06c3075bd40bca685da9da"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982c330cd1bf45cdb53d9a2bcdddfc959e", "guid": "bfdfe7dc352907fc980b868725387e988a8d90e8c814564daf91cf1a77684088", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d41ab09295ca472ad62a4bf4ed90dd0a", "guid": "bfdfe7dc352907fc980b868725387e98bb60988ab34e69fce149fe92301b9536", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984babc17ff3e39e4c859b74e37c839780", "guid": "bfdfe7dc352907fc980b868725387e98370deb057e76181ad9365be822ce9cb2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d3d0eefe7127c6bcb486cdc5993b7289", "guid": "bfdfe7dc352907fc980b868725387e98d5642138b97a2fff34ba32af3d17ce1b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98547a9c091c08aed6232784856424f18d", "guid": "bfdfe7dc352907fc980b868725387e983946e5c247ff129f1b0257065bc8e0f9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a16a41a5a4b881668084759231b06dc7", "guid": "bfdfe7dc352907fc980b868725387e98a2f4f84df8492ef39080c16cb259817c", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e987df71b2f54eb30d6a0708e778c4c1c4e", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98bc52aa5f83c5f88442fd9230e6fdc3cf", "guid": "bfdfe7dc352907fc980b868725387e9854750df119f6e3276497f2c989b37a83"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985dc4cd8242c70dc250e7843a91b7b3db", "guid": "bfdfe7dc352907fc980b868725387e98d46aeb0cc73cc5f7eee002567584b024"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98614d585fd5a383930d030a3953b813bf", "guid": "bfdfe7dc352907fc980b868725387e98171baba446c00ae0ddf73ae1b8c295f1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980067e38c577fce9a88f4515d627bc815", "guid": "bfdfe7dc352907fc980b868725387e986904142f63024a193fd64c5da734a8d4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b59369ecd2e1e31b9debece8d85ec3b9", "guid": "bfdfe7dc352907fc980b868725387e98ae895f5c3fc81477d3f6646e49859573"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dce0e0e2ddf347ce8361ac90c0083065", "guid": "bfdfe7dc352907fc980b868725387e98cf8518037372b1083c6abb682e073efc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9810181a0a5c7e5af1e271de4b95d729c6", "guid": "bfdfe7dc352907fc980b868725387e988da846663497c4b0d054b9d7e7d9bc85"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98053ebd0efbfb6284e0a674e3a0b233c9", "guid": "bfdfe7dc352907fc980b868725387e98bc83cfb2990db31cf7419d7e0b040792"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9834bfbbed7e380d3cb7b15e23af9d32f1", "guid": "bfdfe7dc352907fc980b868725387e9864131b6cf3eef8aec52af3dca8a8e7b5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981e8aeb25efe5861396883a38f93beb60", "guid": "bfdfe7dc352907fc980b868725387e98f2f2828f57900a2765abb98ddb692cd9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c852b1b36f5ff2a16724ae7cbe351882", "guid": "bfdfe7dc352907fc980b868725387e9861559ba3f80e673c5946883e276f75d9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a3d83d65adf60c37d18cf69974d8b903", "guid": "bfdfe7dc352907fc980b868725387e9807e3207c6fe58e5e2d484666ef24fddb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982e630a351481f7d1c40db0251cb8b04f", "guid": "bfdfe7dc352907fc980b868725387e9813a80695edfb8b1be6ceb5c09d175dfd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9887ebcc1fe1c41e6a25cf68c7b0d5dbdc", "guid": "bfdfe7dc352907fc980b868725387e98b9e1c874b33f0ca63f3913cfe9173c90"}], "guid": "bfdfe7dc352907fc980b868725387e984abb43884dec7ad62968511c5f40653e", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9823eca4b3b33af74b846381c4acc45081", "guid": "bfdfe7dc352907fc980b868725387e982ace08dce1d566536aa7dceeea63cbbb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984fd2b7e4839255245a7de0ddb837dec7", "guid": "bfdfe7dc352907fc980b868725387e98e619bbcaf1599dda43598bd831ff5c74"}], "guid": "bfdfe7dc352907fc980b868725387e9822f36a09799847797979b5e4b6c9e2fe", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98bb186436391d0411711ff80ccf1d707a", "targetReference": "bfdfe7dc352907fc980b868725387e98678fb6500ea02c78520816441717cc14"}], "guid": "bfdfe7dc352907fc980b868725387e986b577232ab77fa1d7cac5bc9be7403cb", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98678fb6500ea02c78520816441717cc14", "name": "FirebaseCore-FirebaseCore_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98020791fd2e7b7ddc8fb2658339c42e16", "name": "FirebaseCoreInternal"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}], "guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e988ae261e418baab0fdd0a48d117fe7fa2", "name": "FirebaseCore.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}