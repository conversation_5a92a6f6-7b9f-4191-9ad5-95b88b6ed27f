{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98e9314364f903a6b3ce31356c682e53b9", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98a3bc5e9c2c8b1bbea4c6bcddea36c537", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98bc9feda62fc7896b2feba4ebf10841d1", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98dddd37e8b572cbd4168704ab5908de22", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98bc9feda62fc7896b2feba4ebf10841d1", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e985cb46c70468f9fb89190fb1467dd03ba", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9808fa3221ab7e0eae288cadf0a0fc82aa", "guid": "bfdfe7dc352907fc980b868725387e98179876a0e963f05fc0ce45b816ac6913", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9883a238c410a8e47eb89a66591ed9ffb1", "guid": "bfdfe7dc352907fc980b868725387e980b4ea50925f57ac9109450f04476668d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980bde84344973534fbe8a9589445dfd81", "guid": "bfdfe7dc352907fc980b868725387e98b7ec7ef9db261b90672d584729082657", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980f3dbe2a57f37678ce9ed4eb9c735c42", "guid": "bfdfe7dc352907fc980b868725387e98c6147c01531c030ddf4ef1e887c416a3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986b431595bc2ab8261f65843c72ae124c", "guid": "bfdfe7dc352907fc980b868725387e98cbaf55f692c1a299fedf3805767b3d3c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9819b978c54e8e76c541eed9a5cc480ebc", "guid": "bfdfe7dc352907fc980b868725387e98a3bc2dd97efce75fab04aac23526321c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9834187abe4b583fe02d3a45771ae4d152", "guid": "bfdfe7dc352907fc980b868725387e980a9cbf33e4041bbced33ab57839dbf75", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aeb5366c0abee0cefdf9626f6c012e11", "guid": "bfdfe7dc352907fc980b868725387e98473be042a6ba0b489bfa7b1bf608c3b5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b28bdaf4028055ff50888843101f2083", "guid": "bfdfe7dc352907fc980b868725387e98a74f9f9c55b49e8b9874feb1ce84f6db", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9890de415dc452467d768b8a391063f0ee", "guid": "bfdfe7dc352907fc980b868725387e98216bb4da9a27fcf791bc14098c7ef6a7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982cad2c4b74c86ee9df6c17fe3fa7a654", "guid": "bfdfe7dc352907fc980b868725387e98387582a42876df866e986f0d7498afd0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98857bf6a422f960007413e15d813e9d72", "guid": "bfdfe7dc352907fc980b868725387e986cc0787e97242b00f058263df40ac3c3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986644986a3cfde28fe05f646162078e46", "guid": "bfdfe7dc352907fc980b868725387e9887f7f604f0314e09fc00ffd179ec71b3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a7d958b4eb0b235b8f4ff561469e2ebe", "guid": "bfdfe7dc352907fc980b868725387e98f1d97d20c9f8e06c9bba02b7a491203b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98619c9ee4c17501fa41f1447232434a2a", "guid": "bfdfe7dc352907fc980b868725387e98144b04f19bf2634f4c4f91ecc8b7e09c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ca8736c92bca5832e72ffd7e08fdf316", "guid": "bfdfe7dc352907fc980b868725387e9809e7b5afbf75ae170e9b1852dbcbf4e0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e664ba1953f9dc65074ca16087b04258", "guid": "bfdfe7dc352907fc980b868725387e98c7ad1d8b4c4a00c55897600d35b305a3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9871c3e95a196131f4f2f05e10f6918591", "guid": "bfdfe7dc352907fc980b868725387e98b5b9d4a17b7b9db9ab1926fae582b733", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9897c7a1323a06a4c26f4106d092a46ec9", "guid": "bfdfe7dc352907fc980b868725387e986818ebe40fd343ded85ab7d2f8fec7d5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ae57fd2c4fe2acb04ee32b58bb505ac1", "guid": "bfdfe7dc352907fc980b868725387e9860568e9b8fafba0e5da4b5a7df033e77", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aab7f61ab68f3c68c20da86291412dce", "guid": "bfdfe7dc352907fc980b868725387e98e751495da569da2b8be3f9bf831896f1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9878978cf472f98451f947a8b8b4d57bde", "guid": "bfdfe7dc352907fc980b868725387e989cf4fc65b59252ee00bbfed9a734f26d", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e985e74bde9c07638b4dc4448e3f16d7bf2", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98926e045ffc2651d92a715ae13ef68533", "guid": "bfdfe7dc352907fc980b868725387e982448799228bdb4c81fe194d53702be84"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988fc25719d096b33f28ae97863311c550", "guid": "bfdfe7dc352907fc980b868725387e987df9ac4e8c341aba4863daf6e62ea068"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9869bab70bed6a417e04438f0adc5afac7", "guid": "bfdfe7dc352907fc980b868725387e98d1487a897dadff06f4ff56967c1bb306"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b5d1ff422a261450b934906a57679cf7", "guid": "bfdfe7dc352907fc980b868725387e98fa5275383f5193da2785a6b35b530c71"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d1fa83042cf907fdff9089b87b1cbc0c", "guid": "bfdfe7dc352907fc980b868725387e98ad9c523e82e86d27094758fe795c37ab"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981215d0d931b4b4a2246f3fb99a6552ce", "guid": "bfdfe7dc352907fc980b868725387e98f05191ce9c2659096a8289de5ab7b10a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98219e1ed1dcbb39d0e5cec5255ff2fe7f", "guid": "bfdfe7dc352907fc980b868725387e9883d1cb60a2029be77e4c28672742fde6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9873be532db01ad17973a063e058b2841d", "guid": "bfdfe7dc352907fc980b868725387e983e23f5a01a23593b356a22cb9e22e4a4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982e3e11d1737b90b182c71e9633ed0f79", "guid": "bfdfe7dc352907fc980b868725387e98cb02d107142463346b4caaad7711bd6b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9849de3bcbf40b7fa59b8f32ce41db0421", "guid": "bfdfe7dc352907fc980b868725387e98c01b789a811cd1f4eba526b72de423b1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981f779ceb1fa49f3af77f025f7ad107ae", "guid": "bfdfe7dc352907fc980b868725387e9862bd6b4f183a878ded17506e8d99bff8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9802babc1d378b2f56e0bf510655928d77", "guid": "bfdfe7dc352907fc980b868725387e98cbf0df7f45e40c9cb9f779b635154ae1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a9611c29981896eb82f347670adfcbd7", "guid": "bfdfe7dc352907fc980b868725387e98d7160df197070a62ac58386fa8959474"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984e4b6c01715a26dda0ecc2a37ca367b5", "guid": "bfdfe7dc352907fc980b868725387e98235d7765728d642d1be6c457c88914b8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f53911ee4e8a19928af5f38b553c2ff2", "guid": "bfdfe7dc352907fc980b868725387e986856b67b2be64fcc84e50be9caa7a339"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983a558c829ca82afdc61226ac52656af6", "guid": "bfdfe7dc352907fc980b868725387e9855fbd1df284465e04818bfc37f7e65dd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9849c6f0c89bc6e72f21abc27a6433e14a", "guid": "bfdfe7dc352907fc980b868725387e9842e283ebb0e0d8791d5782d58783aed8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98586a2c83f25e86647896072cc5996ab5", "guid": "bfdfe7dc352907fc980b868725387e98914825030ab0b92d8bd47df5b7fce459"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98228193613a12586e62d8c3432d51f703", "guid": "bfdfe7dc352907fc980b868725387e9894bce3bcbdb9d6869c7ce04b59f2bbcb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986bca79e759cd77141a4b69b3a7044fd7", "guid": "bfdfe7dc352907fc980b868725387e981f3daf1185091339dd5a6064a9f8dd6e"}], "guid": "bfdfe7dc352907fc980b868725387e98b0fe692ef5dc09ff27dc6578e245a1c1", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9823eca4b3b33af74b846381c4acc45081", "guid": "bfdfe7dc352907fc980b868725387e989a7394acb3d1eeb39337627e4d2ef109"}], "guid": "bfdfe7dc352907fc980b868725387e985bfad968749320011c3206fec78792a0", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e9823aa39a912c7e7da0b92855a8e63d048", "targetReference": "bfdfe7dc352907fc980b868725387e98ad53226b339581a6725de188f2c8f823"}], "guid": "bfdfe7dc352907fc980b868725387e981c0ff3c70942d7036c8e1bc6d1549d14", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98ad53226b339581a6725de188f2c8f823", "name": "PromisesObjC-FBLPromises_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98f10882e1684b8a3dfdec597bc0a47af3", "name": "PromisesObjC", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e981c795e45f8d875aac88217c6a2a95faa", "name": "FBLPromises.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}