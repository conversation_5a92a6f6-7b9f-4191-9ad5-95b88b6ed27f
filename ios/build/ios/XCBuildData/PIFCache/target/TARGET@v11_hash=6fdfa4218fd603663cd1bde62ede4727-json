{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98d6e624b1f94c2782f78f04ee3fdde799", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "GTMSessionFetcher", "PRODUCT_NAME": "GTMSessionFetcher", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e986c2c34a44b9c55e1abe25df186faff08", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e981a23888949906dc52ae81f549583bcdf", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher.modulemap", "PRODUCT_MODULE_NAME": "GTMSessionFetcher", "PRODUCT_NAME": "GTMSessionFetcher", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98f93ac27d21fbe3846c7f08fb0528e844", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e981a23888949906dc52ae81f549583bcdf", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher.modulemap", "PRODUCT_MODULE_NAME": "GTMSessionFetcher", "PRODUCT_NAME": "GTMSessionFetcher", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98b267563e348139de22a47d77860b35c7", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e987d3aa6ee4a01e9a51140d5308b665e03", "guid": "bfdfe7dc352907fc980b868725387e98461fe5254345943372e6e8d5fb918f6c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c305e12b3712ad025fa44ec22ae8bfce", "guid": "bfdfe7dc352907fc980b868725387e983a4e2326980ec4d940f5f7bf78bffe26", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e8decebad77ed21f727e2de8ae5516bc", "guid": "bfdfe7dc352907fc980b868725387e98541c1d55ec72c52e16c51f46a852b9cd", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cdd2366735393096a22850b6bb76ec04", "guid": "bfdfe7dc352907fc980b868725387e98b5fd9e033f4bb78fa06dfd73e569bde6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980efb7436c31bb4904721471b445f5df0", "guid": "bfdfe7dc352907fc980b868725387e98d3c11c53266772ee7c707feb8aeb0fb2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980d3a1f0f7691ebcfbb7dad8fad9748d9", "guid": "bfdfe7dc352907fc980b868725387e98257cd54021043bff9e4e9e51280581ec", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98886caae31b34f513221d2f9d8d5cdb8b", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e989c1f1564fce7d383de74154e545c1b41", "guid": "bfdfe7dc352907fc980b868725387e982dc8bc675017d22ca8b0a4e55e2d27c3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98de03747ca846cd536e01f74e72873533", "guid": "bfdfe7dc352907fc980b868725387e98349d4a4951eeaa41053aea485b38a15b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fa6e63c395e5354fd04056494df57f3c", "guid": "bfdfe7dc352907fc980b868725387e98e2f2f08f485d0d7c028e8b5c4dfc3057"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fb953e516149aa13719ff7ae3765a25f", "guid": "bfdfe7dc352907fc980b868725387e98277334a77ba42c0580a883f3d6b3474e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b6497873846cd037794e047632b20a39", "guid": "bfdfe7dc352907fc980b868725387e988d03bfa63a4222bd1f84f19572950c51"}], "guid": "bfdfe7dc352907fc980b868725387e981ea488051a2826a4b8869dac9517473f", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9823eca4b3b33af74b846381c4acc45081", "guid": "bfdfe7dc352907fc980b868725387e982d2047354adb02b970262581bbf0b395"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9818e1d7dfe2849b95e08804531e42cab8", "guid": "bfdfe7dc352907fc980b868725387e9822cdb3f6cea536b6e732216d6352a7fa"}], "guid": "bfdfe7dc352907fc980b868725387e985fa8985d1bf5eefcbc52b1baf8dafb0e", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e9820e3ef7460d4d0c59604ab0c5867de60", "targetReference": "bfdfe7dc352907fc980b868725387e9801af34ddea6be97d757786022edb34b1"}], "guid": "bfdfe7dc352907fc980b868725387e9801e0df4b70d07e9b39161628f0c2e4c8", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e9801af34ddea6be97d757786022edb34b1", "name": "GTMSessionFetcher-GTMSessionFetcher_Core_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98dd3a6a519ed4181bf31ea6bc1f18ebc5", "name": "GTMSessionFetcher", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98f65e88472d384b1ba0888326befb3a8e", "name": "GTMSessionFetcher.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}