import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:universal_platform/universal_platform.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:url_strategy/url_strategy.dart';

// Conditional import for web-specific functionality
import 'dart:html' as html show window, navigator
    if (dart.library.io) 'dart:io' show Platform;

//! git commit --amend --author="Idea2App <<EMAIL>>"
//! GIT_SSH_COMMAND="ssh -i ~/.ssh/id_rsa_idea2app" git push --force origin main

Future main() async {
  WidgetsFlutterBinding.ensureInitialized();

  if (kIsWeb) {
    setPathUrlStrategy();

    await _handlePlatformRedirection();
  }

  runApp(const LinkConverterApp());
}

Future<void> _handlePlatformRedirection() async {
  try {
    String redirectUrl;
    String detectedPlatform = 'unknown';

    if (kIsWeb) {
      // Enhanced web-based platform detection
      final userAgent = html.navigator.userAgent;
      final platform = html.navigator.platform ?? '';

      debugPrint('UserAgent: $userAgent');
      debugPrint('Platform: $platform');

      // More accurate mobile detection for web
      final isIOSDevice = RegExp(r'iPad|iPhone|iPod').hasMatch(userAgent) &&
          !userAgent.contains('MSStream');
      final isAndroidDevice =
          RegExp(r'Android', caseSensitive: false).hasMatch(userAgent);
      final isMacDevice = platform.contains('Mac') && !isIOSDevice;
      final isWindowsDevice = platform.contains('Win');

      // Specific iOS device detection
      final isIPad = userAgent.contains('iPad') ||
          (platform == 'MacIntel' &&
              html.navigator.maxTouchPoints != null &&
              html.navigator.maxTouchPoints! > 1);
      final isIPhone = userAgent.contains('iPhone');

      // Check for mobile browsers and in-app browsers (like Instagram, Facebook, etc.)
      final isMobileBrowser = RegExp(
              r'Mobile|Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini',
              caseSensitive: false)
          .hasMatch(userAgent);
      final isInAppBrowser = RegExp(
              r'Instagram|FBAN|FBAV|Twitter|LinkedIn|WhatsApp',
              caseSensitive: false)
          .hasMatch(userAgent);

      debugPrint(
          'Detected - iOS: $isIOSDevice, Android: $isAndroidDevice, Mac: $isMacDevice, Windows: $isWindowsDevice');
      debugPrint('Specific - iPad: $isIPad, iPhone: $isIPhone');
      debugPrint(
          'Mobile Browser: $isMobileBrowser, In-App Browser: $isInAppBrowser');

      if (isIPad) {
        redirectUrl =
            'https://apps.apple.com/us/app/idea2app-vendor/id6479177341?platform=ipad';
        detectedPlatform = 'iPad';
      } else if (isIPhone || isIOSDevice) {
        redirectUrl =
            'https://apps.apple.com/us/app/idea2app-vendor/id6479177341?platform=iphone';
        detectedPlatform = 'iPhone';
      } else if (isAndroidDevice) {
        redirectUrl =
            'https://play.google.com/store/apps/details?id=com.idea2app.vendor_app';
        detectedPlatform = 'Android';
      } else if (isMacDevice) {
        // Mac users get iPad app store
        redirectUrl =
            'https://apps.apple.com/us/app/idea2app-vendor/id6479177341?platform=ipad';
        detectedPlatform = 'Mac';
      } else {
        // Windows and other platforms
        redirectUrl = 'https://vendor.idea2app.tech';
        detectedPlatform = 'Desktop/Other';
      }
    } else {
      // Fallback to UniversalPlatform for non-web platforms
      if (UniversalPlatform.isIOS) {
        redirectUrl =
            'https://apps.apple.com/us/app/idea2app-vendor/id6479177341?platform=iphone';
        detectedPlatform = 'iOS';
      } else if (UniversalPlatform.isMacOS) {
        redirectUrl =
            'https://apps.apple.com/us/app/idea2app-vendor/id6479177341?platform=ipad';
        detectedPlatform = 'macOS';
      } else if (UniversalPlatform.isAndroid) {
        redirectUrl =
            'https://play.google.com/store/apps/details?id=com.idea2app.vendor_app';
        detectedPlatform = 'Android';
      } else {
        redirectUrl = 'https://vendor.idea2app.tech';
        detectedPlatform = 'Other';
      }
    }

    debugPrint('Detected Platform: $detectedPlatform');
    debugPrint('Redirecting to: $redirectUrl');

    final uri = Uri.parse(redirectUrl);
    if (await canLaunchUrl(uri)) {
      await launchUrl(uri, mode: LaunchMode.platformDefault);
    } else {
      debugPrint('Cannot launch URL: $redirectUrl');
      // Fallback: try to open in same window for web
      if (kIsWeb) {
        html.window.location.href = redirectUrl;
      }
    }
  } catch (e) {
    // If redirection fails, show the app
    debugPrint('Redirection failed: $e');
    // Fallback: try direct window location change for web
    if (kIsWeb) {
      try {
        html.window.location.href = 'https://vendor.idea2app.tech';
      } catch (fallbackError) {
        debugPrint('Fallback redirection also failed: $fallbackError');
      }
    }
  }
}

class LinkConverterApp extends StatelessWidget {
  const LinkConverterApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Idea2App Vendor',
      debugShowCheckedModeBanner: false,
      home: const RedirectionPage(),
    );
  }
}

class RedirectionPage extends StatefulWidget {
  const RedirectionPage({super.key});

  @override
  State<RedirectionPage> createState() => _RedirectionPageState();
}

class _RedirectionPageState extends State<RedirectionPage> {
  String _status = 'Detecting platform...';

  @override
  void initState() {
    super.initState();
    // Immediate redirection attempt
    _redirectUser();
  }

  Future<void> _redirectUser() async {
    setState(() {
      _status = 'Detecting your platform...';
    });

    // Small delay to show the detection message
    await Future.delayed(const Duration(milliseconds: 500));

    setState(() {
      _status = 'Redirecting to the appropriate store...';
    });

    await _handlePlatformRedirection();

    // If we reach here, redirection might have failed
    setState(() {
      _status = 'If you are not redirected automatically, please choose your platform below.';
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: Center(
        child: Padding(
          padding: const EdgeInsets.all(20.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const CircularProgressIndicator(
                color: Color(0xFF25d366),
              ),
              const SizedBox(height: 20),
              const Text(
                'Idea2App Vendor',
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: Color(0xFF25d366),
                ),
              ),
              const SizedBox(height: 20),
              Text(
                _status,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 30),
              // Manual download buttons as fallback
              if (_status.contains('choose your platform'))
                Column(
                  children: [
                    const Text(
                      'Choose your platform:',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(height: 20),
                    _buildDownloadButton(
                      'Download for iPhone',
                      'https://apps.apple.com/us/app/idea2app-vendor/id6479177341?platform=iphone',
                      const Color(0xFF007AFF),
                    ),
                    const SizedBox(height: 10),
                    _buildDownloadButton(
                      'Download for iPad/Mac',
                      'https://apps.apple.com/us/app/idea2app-vendor/id6479177341?platform=ipad',
                      const Color(0xFF007AFF),
                    ),
                    const SizedBox(height: 10),
                    _buildDownloadButton(
                      'Download for Android',
                      'https://play.google.com/store/apps/details?id=com.idea2app.vendor_app',
                      const Color(0xFF34A853),
                    ),
                    const SizedBox(height: 10),
                    _buildDownloadButton(
                      'Visit Website',
                      'https://vendor.idea2app.tech',
                      const Color(0xFF666666),
                    ),
                  ],
                ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDownloadButton(String text, String url, Color color) {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton(
        onPressed: () async {
          final uri = Uri.parse(url);
          if (await canLaunchUrl(uri)) {
            await launchUrl(uri, mode: LaunchMode.platformDefault);
          }
        },
        style: ElevatedButton.styleFrom(
          backgroundColor: color,
          foregroundColor: Colors.white,
          padding: const EdgeInsets.symmetric(vertical: 15),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
        child: Text(
          text,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w500,
          ),
        ),
      ),
    );
  }
}
