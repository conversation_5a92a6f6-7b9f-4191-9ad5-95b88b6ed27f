import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:universal_platform/universal_platform.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:url_strategy/url_strategy.dart';

//! git commit --amend --author="Idea2App <<EMAIL>>"
//! GIT_SSH_COMMAND="ssh -i ~/.ssh/id_rsa_idea2app" git push --force origin main

Future main() async {
  WidgetsFlutterBinding.ensureInitialized();

  if (kIsWeb) {
    setPathUrlStrategy();

    await _handlePlatformRedirection();
  }

  runApp(const LinkConverterApp());
}

Future<void> _handlePlatformRedirection() async {
  try {
    String redirectUrl;

    if (UniversalPlatform.isIOS) {
      // Check if it's iPad or iPhone
      // Note: On web, we can't perfectly distinguish iPad from iPhone
      // so we default to iPhone for iOS devices
      redirectUrl =
          'https://apps.apple.com/us/app/idea2app-vendor/id6479177341?platform=iphone';
    } else if (UniversalPlatform.isMacOS) {
      // Mac users get iPad app store
      redirectUrl =
          'https://apps.apple.com/us/app/idea2app-vendor/id6479177341?platform=ipad';
    } else if (UniversalPlatform.isAndroid) {
      redirectUrl =
          'https://play.google.com/store/apps/details?id=com.idea2app.vendor_app';
    } else {
      // Windows and other platforms
      redirectUrl = 'https://vendor.idea2app.tech';
    }

    debugPrint('Redirecting_To: $redirectUrl');
    final uri = Uri.parse(redirectUrl);
    if (await canLaunchUrl(uri)) {
      await launchUrl(uri, mode: LaunchMode.platformDefault);
    }
  } catch (e) {
    // If redirection fails, show the app
    debugPrint('Redirection failed: $e');
  }
}

class LinkConverterApp extends StatelessWidget {
  const LinkConverterApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Idea2App Vendor',
      debugShowCheckedModeBanner: false,
      home: const RedirectionPage(),
    );
  }
}

class RedirectionPage extends StatefulWidget {
  const RedirectionPage({super.key});

  @override
  State<RedirectionPage> createState() => _RedirectionPageState();
}

class _RedirectionPageState extends State<RedirectionPage> {
  @override
  void initState() {
    super.initState();
    // Additional fallback redirection
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _redirectUser();
    });
  }

  Future<void> _redirectUser() async {
    await _handlePlatformRedirection();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(
              color: Color(0xFF25d366),
            ),
            SizedBox(height: 20),
            Text(
              'Redirecting to Idea2App Vendor...',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w500,
              ),
            ),
            SizedBox(height: 10),
            Text(
              'Please wait while we redirect you to the appropriate platform.',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}
