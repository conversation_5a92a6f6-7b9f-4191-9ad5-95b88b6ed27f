import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import "package:universal_html/html.dart" as html;
import 'package:universal_platform/universal_platform.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:url_strategy/url_strategy.dart';

//! git commit --amend --author="Idea2App <<EMAIL>>"
//! GIT_SSH_COMMAND="ssh -i ~/.ssh/id_rsa_idea2app" git push --force origin main

Future main() async {
  WidgetsFlutterBinding.ensureInitialized();

  setPathUrlStrategy();

  await _handlePlatformRedirection();

  runApp(const LinkConverterApp());
}

bool _isMacOSWeb() {
  return defaultTargetPlatform == TargetPlatform.macOS &&
      UniversalPlatform.isWeb;
}

bool _isIOSWeb() {
  final userAgent = html.window.navigator.userAgent.toLowerCase();
  return userAgent.contains("iphone") || userAgent.contains("ipad");
}

Future<void> _handlePlatformRedirection() async {
  try {
    String redirectUrl;
    String detectedPlatform;

    if (UniversalPlatform.isAndroid) {
      redirectUrl =
          'https://play.google.com/store/apps/details?id=com.idea2app.vendor_app';
      detectedPlatform = 'Android';
    } else if (UniversalPlatform.isIOS) {
      redirectUrl =
          'https://apps.apple.com/us/app/idea2app-vendor/id6479177341?platform=iphone';
      detectedPlatform = 'iOS';
    } else if (UniversalPlatform.isWeb && _isIOSWeb()) {
      redirectUrl =
          'https://apps.apple.com/us/app/idea2app-vendor/id6479177341?platform=iphone';
      detectedPlatform = 'iOS (Web)';
    } else if (UniversalPlatform.isWeb && _isMacOSWeb()) {
      redirectUrl =
          'https://apps.apple.com/us/app/idea2app-vendor/id6479177341?platform=ipad';
      detectedPlatform = 'macOS (Web)';
    } else {
      redirectUrl = 'https://vendor.idea2app.tech';
      detectedPlatform = 'Desktop/Other';
    }

    debugPrint('Detected Platform: $detectedPlatform');
    debugPrint('Redirecting to: $redirectUrl');
    debugPrint(
        'DDDD ${defaultTargetPlatform} ISS_to: ${TargetPlatform.macOS} ${UniversalPlatform.isWeb} iSSEQ  ${defaultTargetPlatform == TargetPlatform.macOS}');

    html.window.location.href = redirectUrl;
  } catch (e) {
    debugPrint('Redirection failed: $e');
  }
}

// Future<void> _handlePlatformRedirection() async {
//   try {
//     String redirectUrl;
//     String detectedPlatform;
//
//     // Use UniversalPlatform for reliable platform detection
//     if (UniversalPlatform.isAndroid) {
//       redirectUrl =
//           'https://play.google.com/store/apps/details?id=com.idea2app.vendor_app';
//       detectedPlatform = 'Android';
//     } else if (UniversalPlatform.isIOS) {
//       // For iOS, default to iPhone App Store
//       redirectUrl =
//           'https://apps.apple.com/us/app/idea2app-vendor/id6479177341?platform=iphone';
//       detectedPlatform = 'iOS';
//     } else if (UniversalPlatform.isMacOS) {
//       // Mac users get iPad app store
//       redirectUrl =
//           'https://apps.apple.com/us/app/idea2app-vendor/id6479177341?platform=ipad';
//       detectedPlatform = 'macOS';
//     } else {
//       // Windows, Linux, and other platforms go to website
//       redirectUrl = 'https://vendor.idea2app.tech';
//       detectedPlatform = 'Desktop/Other';
//     }
//
//     debugPrint('Detected Platform: $detectedPlatform');
//     debugPrint('Redirecting to: $redirectUrl');
//
//     final uri = Uri.parse(redirectUrl);
//     if (await canLaunchUrl(uri)) {
//       await launchUrl(uri, mode: LaunchMode.platformDefault);
//     } else {
//       debugPrint('Cannot launch URL: $redirectUrl');
//     }
//   } catch (e) {
//     debugPrint('Redirection failed: $e');
//   }
// }

class LinkConverterApp extends StatelessWidget {
  const LinkConverterApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Idea2App Vendor',
      debugShowCheckedModeBanner: false,
      home: Material(
        color: Colors.white,
      ),
    );
  }
}

class RedirectionPage extends StatefulWidget {
  const RedirectionPage({super.key});

  @override
  State<RedirectionPage> createState() => _RedirectionPageState();
}

class _RedirectionPageState extends State<RedirectionPage> {
  String _status = 'Detecting platform...';
  bool _showManualOptions = false;

  @override
  void initState() {
    super.initState();
    _redirectUser();
  }

  Future<void> _redirectUser() async {
    setState(() {
      _status = 'Detecting your platform...';
    });

    // Small delay to show the detection message
    await Future.delayed(const Duration(milliseconds: 300));

    setState(() {
      _status = 'Redirecting to the appropriate store...';
    });

    await _handlePlatformRedirection();

    // If we reach here, show manual options after a delay
    await Future.delayed(const Duration(seconds: 2));
    setState(() {
      _status =
          'If you were not redirected automatically, please choose your platform:';
      _showManualOptions = true;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: Center(
        child: Padding(
          padding: const EdgeInsets.all(20.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const CircularProgressIndicator(
                color: Color(0xFF25d366),
              ),
              const SizedBox(height: 20),
              const Text(
                'Idea2App Vendor',
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: Color(0xFF25d366),
                ),
              ),
              const SizedBox(height: 20),
              Text(
                _status,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 30),
              // Manual download buttons as fallback
              if (_showManualOptions)
                Column(
                  children: [
                    const SizedBox(height: 20),
                    _buildDownloadButton(
                      'Download for iPhone',
                      'https://apps.apple.com/us/app/idea2app-vendor/id6479177341?platform=iphone',
                      const Color(0xFF007AFF),
                    ),
                    const SizedBox(height: 10),
                    _buildDownloadButton(
                      'Download for iPad/Mac',
                      'https://apps.apple.com/us/app/idea2app-vendor/id6479177341?platform=ipad',
                      const Color(0xFF007AFF),
                    ),
                    const SizedBox(height: 10),
                    _buildDownloadButton(
                      'Download for Android',
                      'https://play.google.com/store/apps/details?id=com.idea2app.vendor_app',
                      const Color(0xFF34A853),
                    ),
                    const SizedBox(height: 10),
                    _buildDownloadButton(
                      'Visit Website',
                      'https://vendor.idea2app.tech',
                      const Color(0xFF666666),
                    ),
                  ],
                ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDownloadButton(String text, String url, Color color) {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton(
        onPressed: () async {
          final uri = Uri.parse(url);
          if (await canLaunchUrl(uri)) {
            await launchUrl(uri, mode: LaunchMode.platformDefault);
          }
        },
        style: ElevatedButton.styleFrom(
          backgroundColor: color,
          foregroundColor: Colors.white,
          padding: const EdgeInsets.symmetric(vertical: 15),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
        child: Text(
          text,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w500,
          ),
        ),
      ),
    );
  }
}
