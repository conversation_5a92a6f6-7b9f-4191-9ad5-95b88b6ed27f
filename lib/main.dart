import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:url_strategy/url_strategy.dart';

//! git commit --amend --author="Idea2App <<EMAIL>>"
//! GIT_SSH_COMMAND="ssh -i ~/.ssh/id_rsa_idea2app" git push --force origin main

Future main() async {
  WidgetsFlutterBinding.ensureInitialized();

  if (kIsWeb) {
    setPathUrlStrategy();
    await _handlePlatformRedirection();
  }

  runApp(const LinkConverterApp());
}

Future<void> _handlePlatformRedirection() async {
  try {
    String redirectUrl;
    String detectedPlatform;

    // Use defaultTargetPlatform for reliable platform detection
    debugPrint(
        'DDDD $defaultTargetPlatform ISS_to: ${TargetPlatform.macOS} $kIsWeb iSSEQ  ${defaultTargetPlatform == TargetPlatform.macOS}');

    if (defaultTargetPlatform == TargetPlatform.android) {
      redirectUrl =
          'https://play.google.com/store/apps/details?id=com.idea2app.vendor_app';
      detectedPlatform = kIsWeb ? 'Android (Web)' : 'Android';
    } else if (defaultTargetPlatform == TargetPlatform.iOS) {
      redirectUrl =
          'https://apps.apple.com/us/app/idea2app-vendor/id6479177341?platform=iphone';
      detectedPlatform = kIsWeb ? 'iOS (Web)' : 'iOS';
    } else if (defaultTargetPlatform == TargetPlatform.macOS) {
      redirectUrl =
          'https://apps.apple.com/us/app/idea2app-vendor/id6479177341?platform=ipad';
      detectedPlatform = kIsWeb ? 'macOS (Web)' : 'macOS';
    } else if (defaultTargetPlatform == TargetPlatform.windows) {
      redirectUrl = 'https://vendor.idea2app.tech';
      detectedPlatform = kIsWeb ? 'Windows (Web)' : 'Windows';
    } else if (defaultTargetPlatform == TargetPlatform.linux) {
      redirectUrl = 'https://vendor.idea2app.tech';
      detectedPlatform = kIsWeb ? 'Linux (Web)' : 'Linux';
    } else {
      redirectUrl = 'https://vendor.idea2app.tech';
      detectedPlatform = kIsWeb ? 'Unknown (Web)' : 'Unknown';
    }

    debugPrint('Detected Platform: $detectedPlatform');
    debugPrint('Redirecting to: $redirectUrl');

    final uri = Uri.parse(redirectUrl);
    if (await canLaunchUrl(uri)) {
      await launchUrl(uri, mode: LaunchMode.platformDefault);
    } else {
      debugPrint('Cannot launch URL: $redirectUrl');
    }
  } catch (e) {
    debugPrint('Redirection failed: $e');
  }
}

class LinkConverterApp extends StatelessWidget {
  const LinkConverterApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Idea2App Vendor',
      debugShowCheckedModeBanner: false,
      home: Material(
        color: Colors.white,
      ),
    );
  }
}
